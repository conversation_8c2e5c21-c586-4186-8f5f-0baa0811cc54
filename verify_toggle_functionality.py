#!/usr/bin/env python3
"""
Verify Toggle Functionality - Check if change detection and include/exclude toggles work correctly
"""

import sqlite3
import json
import os

def verify_toggle_functionality():
    print("🔍 VERIFYING TOGGLE FUNCTIONALITY")
    print("=" * 60)
    
    # Check if dictionary file exists
    dict_path = 'core/payroll_dictionary.json'
    if not os.path.exists(dict_path):
        print(f'❌ Dictionary file not found: {dict_path}')
        return False
    
    # Load dictionary to check toggle states
    print(f'\n📋 CHECKING DICTIONARY TOGGLE STATES:')
    try:
        with open(dict_path, 'r') as f:
            dictionary = json.load(f)
        
        # Check each section for toggle states
        toggle_summary = {}
        
        for section_name, section_data in dictionary.items():
            if isinstance(section_data, dict) and 'items' in section_data:
                items = section_data['items']
                section_toggles = {
                    'include_new': 0,
                    'include_increase': 0, 
                    'include_decrease': 0,
                    'exclude_new': 0,
                    'exclude_increase': 0,
                    'exclude_decrease': 0,
                    'total_items': len(items)
                }
                
                for item_name, item_data in items.items():
                    if isinstance(item_data, dict):
                        # Count toggle states
                        if item_data.get('include_new', True):
                            section_toggles['include_new'] += 1
                        else:
                            section_toggles['exclude_new'] += 1
                            
                        if item_data.get('include_increase', True):
                            section_toggles['include_increase'] += 1
                        else:
                            section_toggles['exclude_increase'] += 1
                            
                        if item_data.get('include_decrease', True):
                            section_toggles['include_decrease'] += 1
                        else:
                            section_toggles['exclude_decrease'] += 1
                
                toggle_summary[section_name] = section_toggles
                
                print(f'\n📊 {section_name} SECTION:')
                print(f'   Total items: {section_toggles["total_items"]}')
                print(f'   Include NEW: {section_toggles["include_new"]} | Exclude: {section_toggles["exclude_new"]}')
                print(f'   Include INCREASE: {section_toggles["include_increase"]} | Exclude: {section_toggles["exclude_increase"]}')
                print(f'   Include DECREASE: {section_toggles["include_decrease"]} | Exclude: {section_toggles["exclude_decrease"]}')
        
        print(f'\n✅ Dictionary toggle states loaded successfully')
        
    except Exception as e:
        print(f'❌ Error loading dictionary: {e}')
        return False
    
    # Check database for actual report data vs toggle states
    print(f'\n🔍 CHECKING DATABASE REPORT DATA VS TOGGLE STATES:')
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get latest session
        cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
        session_result = cursor.fetchone()
        if not session_result:
            print('❌ No audit sessions found')
            return False
        
        session_id = session_result[0]
        print(f'📅 Using session: {session_id}')
        
        # Check comparison results by change type
        print(f'\n📊 COMPARISON RESULTS BY CHANGE TYPE:')
        cursor.execute('''
            SELECT change_type, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ?
            GROUP BY change_type
            ORDER BY count DESC
        ''', (session_id,))
        
        change_type_counts = cursor.fetchall()
        total_changes = sum(count for _, count in change_type_counts)
        
        for change_type, count in change_type_counts:
            percentage = (count / total_changes * 100) if total_changes > 0 else 0
            print(f'   {change_type}: {count} ({percentage:.1f}%)')
        
        # Check specific items that should be excluded based on toggle states
        print(f'\n🔍 CHECKING EXCLUDED ITEMS IN REPORTS:')
        
        # Find items that are marked as excluded in dictionary
        excluded_items = []
        for section_name, section_data in dictionary.items():
            if isinstance(section_data, dict) and 'items' in section_data:
                for item_name, item_data in section_data['items'].items():
                    if isinstance(item_data, dict):
                        if not item_data.get('include_new', True):
                            excluded_items.append((item_name, 'NEW', section_name))
                        if not item_data.get('include_increase', True):
                            excluded_items.append((item_name, 'INCREASED', section_name))
                        if not item_data.get('include_decrease', True):
                            excluded_items.append((item_name, 'DECREASED', section_name))
        
        print(f'   Found {len(excluded_items)} excluded item-change combinations in dictionary')
        
        # Check if these excluded items appear in comparison results
        violations = 0
        for item_name, change_type, section_name in excluded_items[:10]:  # Check first 10
            cursor.execute('''
                SELECT COUNT(*) FROM comparison_results 
                WHERE session_id = ? AND item_label = ? AND change_type = ?
            ''', (session_id, item_name, change_type))
            
            count = cursor.fetchone()[0]
            if count > 0:
                print(f'   🚨 VIOLATION: {item_name} ({change_type}) appears {count} times despite being excluded')
                violations += 1
            else:
                print(f'   ✅ CORRECT: {item_name} ({change_type}) properly excluded')
        
        if violations == 0:
            print(f'\n✅ NO VIOLATIONS: Toggle exclusions are working correctly')
        else:
            print(f'\n❌ {violations} VIOLATIONS: Some excluded items still appear in reports')
        
        conn.close()
        return violations == 0
        
    except Exception as e:
        print(f'❌ Error checking database: {e}')
        return False

def verify_dictionary_manager_effectiveness():
    """Verify if dictionary manager changes hit the database and are effective"""
    print(f'\n🔍 VERIFYING DICTIONARY MANAGER EFFECTIVENESS')
    print("=" * 60)
    
    # Test the specific case: SALARY ADVANCE PENT.
    test_loan = "SALARY ADVANCE PENT."
    
    print(f'🧪 TESTING LOAN CLASSIFICATION: "{test_loan}"')
    
    try:
        # Test with dictionary manager
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from core.dictionary_manager import PayrollDictionaryManager
        
        # Create dictionary manager instance
        dict_manager = PayrollDictionaryManager(debug=True)
        
        # Test classification
        classification = dict_manager.classify_loan_type(test_loan)
        print(f'   📋 Dictionary Manager Classification: {classification}')
        
        # Check if it's in the dictionary
        dictionary = dict_manager.get_dictionary()
        loans_section = dictionary.get('LOANS', {})
        
        # Check fixed items
        fixed_items = []
        items = loans_section.get('items', {})
        for item_name, item_data in items.items():
            if item_data.get('is_fixed', False) and not item_data.get('is_column_header', False):
                fixed_items.append(item_name)
        
        print(f'\n📋 FIXED ITEMS IN LOANS SECTION ({len(fixed_items)}):')
        for item in fixed_items[:10]:  # Show first 10
            print(f'   - {item}')
        
        # Check if our test loan is in fixed items
        test_loan_found = any(test_loan.upper() in item.upper() or item.upper() in test_loan.upper() 
                             for item in fixed_items)
        
        if test_loan_found:
            print(f'\n✅ "{test_loan}" found in dictionary fixed items')
        else:
            print(f'\n❌ "{test_loan}" NOT found in dictionary fixed items')
            print(f'   This explains why it\'s classified as EXTERNAL')
        
        # Check loan_types section
        loan_types = loans_section.get('loan_types', {})
        print(f'\n📋 LOAN TYPES SECTION ({len(loan_types)} types):')
        
        for loan_type_name, loan_type_data in loan_types.items():
            classification_type = loan_type_data.get('classification', 'UNKNOWN')
            items_count = len(loan_type_data.get('items', []))
            print(f'   - {loan_type_name}: {classification_type} ({items_count} items)')
            
            # Check if our test loan is in this type
            if test_loan.upper() in loan_type_name.upper() or loan_type_name.upper() in test_loan.upper():
                print(f'     🎯 MATCH FOUND: "{test_loan}" matches loan type "{loan_type_name}"')
        
        # Check database directly
        print(f'\n🗄️ CHECKING DATABASE DIRECTLY:')
        
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Check if dictionary_items table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dictionary_items'")
        if cursor.fetchone():
            # Check for our loan in database
            cursor.execute('''
                SELECT di.item_name, ds.section_name, di.value_format, di.validation_rules
                FROM dictionary_items di
                JOIN dictionary_sections ds ON di.section_id = ds.id
                WHERE ds.section_name = 'LOANS' AND UPPER(di.item_name) LIKE ?
            ''', (f'%{test_loan.upper()}%',))
            
            db_results = cursor.fetchall()
            if db_results:
                print(f'   ✅ Found {len(db_results)} matching entries in database:')
                for item_name, section, value_format, validation_rules in db_results:
                    print(f'     - {item_name}: {value_format} | {validation_rules}')
            else:
                print(f'   ❌ No matching entries found in database for "{test_loan}"')
        else:
            print(f'   ⚠️ dictionary_items table not found in database')
        
        conn.close()
        
        # Test with actual extraction data
        print(f'\n🔍 CHECKING ACTUAL EXTRACTION DATA:')
        
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get latest session
        cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
        session_result = cursor.fetchone()
        if session_result:
            session_id = session_result[0]
            
            # Look for the test loan in extracted data
            cursor.execute('''
                SELECT employee_id, item_label, item_value, period_type
                FROM extracted_data 
                WHERE session_id = ? AND UPPER(item_label) LIKE ?
                LIMIT 5
            ''', (session_id, f'%{test_loan.upper()}%'))
            
            extraction_results = cursor.fetchall()
            if extraction_results:
                print(f'   ✅ Found {len(extraction_results)} instances in extracted data:')
                for emp_id, item_label, item_value, period_type in extraction_results:
                    print(f'     - {emp_id} ({period_type}): {item_label} = {item_value}')
            else:
                print(f'   ❌ No instances of "{test_loan}" found in extracted data')
        
        conn.close()
        
        return classification == "IN-HOUSE LOAN"
        
    except Exception as e:
        print(f'❌ Error testing dictionary manager: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Verifying toggle functionality and dictionary manager effectiveness...")
    
    # Test 1: Toggle functionality
    toggle_success = verify_toggle_functionality()
    
    # Test 2: Dictionary manager effectiveness
    dict_success = verify_dictionary_manager_effectiveness()
    
    print(f'\n🎯 FINAL RESULTS:')
    print(f'   Toggle functionality: {"✅ WORKING" if toggle_success else "❌ ISSUES FOUND"}')
    print(f'   Dictionary manager: {"✅ WORKING" if dict_success else "❌ ISSUES FOUND"}')
    
    if not toggle_success:
        print(f'\n💡 TOGGLE ISSUES: Some excluded items may still appear in reports')
        print(f'   This could indicate the toggle enforcement is not working properly')
    
    if not dict_success:
        print(f'\n💡 DICTIONARY ISSUES: New in-house loans may not be classified correctly')
        print(f'   This could indicate dictionary changes are not hitting the database')
