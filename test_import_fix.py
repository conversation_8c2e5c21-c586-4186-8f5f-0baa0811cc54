#!/usr/bin/env python3
"""
Test Import Fix for Enhanced Duplicate Checker
"""

import sys
import os

def test_import_fix():
    print("🧪 TESTING IMPORT FIX FOR ENHANCED DUPLICATE CHECKER")
    print("=" * 60)
    
    try:
        # Test the import that was failing
        print("📋 Testing import from phased_process_manager...")
        
        # Add the core directory to path (same as in phased_process_manager.py)
        current_dir = os.path.dirname(os.path.abspath(__file__))
        core_dir = os.path.join(current_dir, 'core')
        
        if core_dir not in sys.path:
            sys.path.insert(0, core_dir)
        
        print(f"   Added to path: {core_dir}")
        
        # Test the import
        from enhanced_duplicate_checker import should_add_to_auto_learning, is_item_duplicate
        
        print("   ✅ Import successful!")
        
        # Test the functions
        print("\n📋 Testing duplicate checker functions...")
        
        # Test with a known item
        is_dup, reason = is_item_duplicate('PERSONAL DETAILS', 'EMPLOYEE NAME')
        should_add, add_reason = should_add_to_auto_learning('PERSONAL DETAILS', 'EMPLOYEE NAME')
        
        print(f"   Test item: PERSONAL DETAILS.EMPLOYEE NAME")
        print(f"   Is duplicate: {is_dup} ({reason})")
        print(f"   Should add to auto-learning: {should_add} ({add_reason})")
        
        if is_dup and not should_add:
            print("   ✅ Duplicate prevention logic working correctly")
        else:
            print("   ⚠️ Duplicate prevention logic may need attention")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        
        # Check if the file exists
        enhanced_checker_path = os.path.join('core', 'enhanced_duplicate_checker.py')
        if os.path.exists(enhanced_checker_path):
            print(f"   ✅ File exists: {enhanced_checker_path}")
        else:
            print(f"   ❌ File missing: {enhanced_checker_path}")
        
        return False
        
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_phased_process_manager_import():
    """Test if phased_process_manager can now import successfully"""
    print(f"\n🧪 TESTING PHASED PROCESS MANAGER IMPORT")
    print("=" * 60)
    
    try:
        # Test importing the phased process manager
        print("📋 Testing phased_process_manager import...")
        
        # Add paths
        current_dir = os.path.dirname(os.path.abspath(__file__))
        core_dir = os.path.join(current_dir, 'core')
        
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        if core_dir not in sys.path:
            sys.path.insert(0, core_dir)
        
        # Import the module
        from core.phased_process_manager import PhasedProcessManager
        
        print("   ✅ Phased process manager import successful!")
        
        # Test creating an instance (without initializing)
        print("📋 Testing class instantiation...")
        
        # This should work without errors
        manager_class = PhasedProcessManager
        print(f"   ✅ Class loaded: {manager_class.__name__}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False
        
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_path_info():
    """Show current Python path information"""
    print(f"\n📋 PYTHON PATH INFORMATION")
    print("=" * 60)
    
    print(f"Current working directory: {os.getcwd()}")
    print(f"Script directory: {os.path.dirname(os.path.abspath(__file__))}")
    
    print(f"\nPython path entries:")
    for i, path in enumerate(sys.path):
        print(f"   {i+1}. {path}")
    
    # Check for core directory
    core_dir = os.path.join(os.getcwd(), 'core')
    print(f"\nCore directory: {core_dir}")
    print(f"Core directory exists: {os.path.exists(core_dir)}")
    
    if os.path.exists(core_dir):
        enhanced_checker = os.path.join(core_dir, 'enhanced_duplicate_checker.py')
        print(f"Enhanced duplicate checker exists: {os.path.exists(enhanced_checker)}")

if __name__ == "__main__":
    print("Testing import fix for enhanced duplicate checker...")
    
    # Show path information
    show_path_info()
    
    # Test 1: Direct import test
    import_success = test_import_fix()
    
    # Test 2: Phased process manager import test
    manager_success = test_phased_process_manager_import()
    
    print(f'\n🎯 TEST RESULTS:')
    print(f'   Direct import test: {"✅ PASS" if import_success else "❌ FAIL"}')
    print(f'   Manager import test: {"✅ PASS" if manager_success else "❌ FAIL"}')
    
    if import_success and manager_success:
        print(f'\n🎉 IMPORT FIX SUCCESSFUL!')
        print(f'   ✅ Enhanced duplicate checker can be imported')
        print(f'   ✅ Phased process manager loads without errors')
        print(f'   ✅ Auto-learning duplicate prevention should work')
    else:
        print(f'\n❌ IMPORT FIX NEEDS MORE WORK')
        if not import_success:
            print(f'   - Enhanced duplicate checker import failed')
        if not manager_success:
            print(f'   - Phased process manager import failed')
        print(f'   - Check Python path and module locations')
