#!/usr/bin/env python3
"""
Test Duplicate Preservation Fix - Test if the extraction now preserves duplicates
"""

from perfect_section_aware_extractor import PerfectSectionAwareExtractor

def test_duplicate_preservation():
    print("🧪 TESTING DUPLICATE PRESERVATION FIX")
    print("=" * 50)
    
    # Test with a simple mock section that has duplicates
    extractor = PerfectSectionAwareExtractor(debug=True)
    
    # Create mock elements that simulate duplicate items
    mock_elements = [
        {'text': 'BASIC SALARY', 'x': 100, 'y': 200},
        {'text': '5,000.00', 'x': 300, 'y': 200},
        {'text': 'BASIC SALARY', 'x': 100, 'y': 220},  # Duplicate label
        {'text': '6,000.00', 'x': 300, 'y': 220},     # Different amount
        {'text': 'INCOME TAX', 'x': 100, 'y': 240},
        {'text': '500.00', 'x': 300, 'y': 240},
        {'text': 'INCOME TAX', 'x': 100, 'y': 260},   # Duplicate label
        {'text': '500.00', 'x': 300, 'y': 260},       # Same amount
    ]
    
    print("\n🔍 Testing _find_label_value_pairs with duplicates:")
    pairs = extractor._find_label_value_pairs(mock_elements, 'EARNINGS')
    
    print(f"\n📊 Results: Found {len(pairs)} pairs")
    for i, pair in enumerate(pairs):
        print(f"  {i+1}. '{pair['label']['text']}' → '{pair['value']['text']}'")
    
    # Check if duplicates were preserved
    basic_salary_count = sum(1 for pair in pairs if 'BASIC SALARY' in pair['label']['text'])
    income_tax_count = sum(1 for pair in pairs if 'INCOME TAX' in pair['label']['text'])
    
    print(f"\n🎯 Duplicate Analysis:")
    print(f"  BASIC SALARY entries: {basic_salary_count} (should be 2)")
    print(f"  INCOME TAX entries: {income_tax_count} (should be 2)")
    
    if basic_salary_count >= 2 and income_tax_count >= 2:
        print("✅ SUCCESS: Duplicates are being preserved!")
        return True
    else:
        print("❌ FAILED: Duplicates are still being removed")
        return False

if __name__ == "__main__":
    success = test_duplicate_preservation()
    if success:
        print("\n🎉 The extraction fix is working - duplicates will now be detected!")
    else:
        print("\n🚨 The extraction fix needs more work")
