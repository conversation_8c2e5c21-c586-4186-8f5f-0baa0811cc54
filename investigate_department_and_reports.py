#!/usr/bin/env python3
"""
Investigate Department Population and Report Generation Issues
"""

import sqlite3
import json
import os
from datetime import datetime

def investigate_department_population():
    """Investigate why department column fails to populate in tracker table"""
    print("🔍 INVESTIGATING DEPARTMENT POPULATION ISSUE")
    print("=" * 60)
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Get latest session
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    if not session_result:
        print('❌ No audit sessions found')
        return False
    
    session_id = session_result[0]
    print(f'📅 Using session: {session_id}')
    
    # Check tracker_results table schema
    print(f'\n📋 TRACKER_RESULTS TABLE SCHEMA:')
    cursor.execute("PRAGMA table_info(tracker_results)")
    columns = cursor.fetchall()
    
    has_department = False
    for col in columns:
        col_name = col[1]
        col_type = col[2]
        print(f'   {col_name}: {col_type}')
        if col_name == 'department':
            has_department = True
    
    if not has_department:
        print(f'❌ Department column missing from tracker_results table!')
        return False
    
    # Check current department data in tracker_results
    print(f'\n📊 CURRENT DEPARTMENT DATA IN TRACKER_RESULTS:')
    cursor.execute('''
        SELECT employee_id, department, COUNT(*) as count
        FROM tracker_results 
        WHERE session_id = ?
        GROUP BY employee_id, department
        ORDER BY employee_id
        LIMIT 10
    ''', (session_id,))
    
    tracker_dept_data = cursor.fetchall()
    
    if tracker_dept_data:
        print(f'Found {len(tracker_dept_data)} tracker records with department data:')
        null_dept_count = 0
        unknown_dept_count = 0
        valid_dept_count = 0
        
        for emp_id, dept, count in tracker_dept_data:
            if dept is None:
                null_dept_count += count
                print(f'   {emp_id}: NULL ({count} records) ❌')
            elif dept in ['Unknown', 'DEPARTMENT_PENDING_VERIFICATION', 'DEPARTMENT_LOOKUP_ERROR']:
                unknown_dept_count += count
                print(f'   {emp_id}: {dept} ({count} records) ⚠️')
            else:
                valid_dept_count += count
                print(f'   {emp_id}: {dept} ({count} records) ✅')
        
        print(f'\n📊 DEPARTMENT DATA SUMMARY:')
        print(f'   Valid departments: {valid_dept_count}')
        print(f'   Unknown/pending: {unknown_dept_count}')
        print(f'   NULL departments: {null_dept_count}')
        
        if null_dept_count > 0 or unknown_dept_count > 0:
            print(f'🚨 ISSUE: {null_dept_count + unknown_dept_count} records have missing/invalid departments')
    else:
        print(f'❌ No tracker records found with department data')
    
    # Check available department sources
    print(f'\n🔍 CHECKING DEPARTMENT DATA SOURCES:')
    
    # Source 1: extracted_data PERSONAL DETAILS section
    print(f'\n   📋 SOURCE 1: EXTRACTED_DATA (PERSONAL DETAILS):')
    cursor.execute('''
        SELECT employee_id, item_value as department, period_type
        FROM extracted_data 
        WHERE session_id = ? AND section_name = 'PERSONAL DETAILS' 
        AND (item_label = 'DEPARTMENT' OR item_label = 'Department')
        ORDER BY employee_id, period_type
        LIMIT 10
    ''', (session_id,))
    
    extracted_dept_data = cursor.fetchall()
    
    if extracted_dept_data:
        print(f'      Found {len(extracted_dept_data)} department records in extracted_data:')
        for emp_id, dept, period in extracted_dept_data:
            print(f'         {emp_id} ({period}): {dept}')
    else:
        print(f'      ❌ No department data found in extracted_data PERSONAL DETAILS')
        
        # Check what items are available in PERSONAL DETAILS
        cursor.execute('''
            SELECT DISTINCT item_label
            FROM extracted_data 
            WHERE session_id = ? AND section_name = 'PERSONAL DETAILS'
        ''', (session_id,))
        
        available_items = cursor.fetchall()
        print(f'      Available PERSONAL DETAILS items: {[item[0] for item in available_items]}')
    
    # Source 2: Check if there's an employees table
    print(f'\n   📋 SOURCE 2: EMPLOYEES TABLE:')
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='employees'")
    if cursor.fetchone():
        cursor.execute('SELECT employee_id, department FROM employees LIMIT 5')
        emp_table_data = cursor.fetchall()
        if emp_table_data:
            print(f'      Found {len(emp_table_data)} records in employees table:')
            for emp_id, dept in emp_table_data:
                print(f'         {emp_id}: {dept}')
        else:
            print(f'      ❌ Employees table exists but is empty')
    else:
        print(f'      ❌ No employees table found')
    
    # Source 3: Check other sections for department-like data
    print(f'\n   📋 SOURCE 3: OTHER SECTIONS WITH DEPARTMENT DATA:')
    cursor.execute('''
        SELECT DISTINCT section_name, item_label, COUNT(*) as count
        FROM extracted_data 
        WHERE session_id = ? AND (
            UPPER(item_label) LIKE '%DEPARTMENT%' OR 
            UPPER(item_label) LIKE '%DEPT%' OR
            UPPER(item_value) LIKE '%DEPARTMENT%'
        )
        GROUP BY section_name, item_label
        ORDER BY count DESC
    ''', (session_id,))
    
    other_dept_sources = cursor.fetchall()
    
    if other_dept_sources:
        print(f'      Found {len(other_dept_sources)} other department-related sources:')
        for section, item, count in other_dept_sources:
            print(f'         {section}.{item}: {count} records')
    else:
        print(f'      ❌ No other department-related data found')
    
    conn.close()
    
    # Determine the best source for department data
    print(f'\n💡 DEPARTMENT DATA SOURCE ANALYSIS:')
    if extracted_dept_data:
        print(f'   ✅ BEST SOURCE: extracted_data PERSONAL DETAILS section')
        print(f'   📋 Recommendation: Use PERSONAL DETAILS.DEPARTMENT for reliable population')
        return True
    elif other_dept_sources:
        print(f'   ⚠️ ALTERNATIVE SOURCE: Other sections contain department data')
        print(f'   📋 Recommendation: Use alternative section for department population')
        return True
    else:
        print(f'   ❌ NO RELIABLE SOURCE: No department data found in any source')
        print(f'   📋 Recommendation: Check extraction process for department data')
        return False

def investigate_report_generation():
    """Investigate how reports are generated and saved in Report Manager"""
    print(f'\n🔍 INVESTIGATING REPORT GENERATION SYSTEM')
    print("=" * 60)
    
    # Check if Report Manager database exists
    report_db_path = 'data/templar_payroll_auditor.db'
    
    if not os.path.exists(report_db_path):
        print(f'❌ Report database not found: {report_db_path}')
        return False
    
    conn = sqlite3.connect(report_db_path)
    cursor = conn.cursor()
    
    # Check reports table schema
    print(f'\n📋 REPORTS TABLE SCHEMA:')
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='reports'")
    if cursor.fetchone():
        cursor.execute("PRAGMA table_info(reports)")
        columns = cursor.fetchall()
        
        for col in columns:
            col_name = col[1]
            col_type = col[2]
            print(f'   {col_name}: {col_type}')
    else:
        print(f'❌ Reports table not found in database')
        return False
    
    # Check existing reports
    print(f'\n📊 EXISTING REPORTS IN DATABASE:')
    cursor.execute('''
        SELECT report_id, report_type, report_category, title, created_at
        FROM reports 
        ORDER BY created_at DESC 
        LIMIT 10
    ''')
    
    existing_reports = cursor.fetchall()
    
    if existing_reports:
        print(f'Found {len(existing_reports)} existing reports:')
        for report_id, report_type, category, title, created_at in existing_reports:
            print(f'   {report_id}: {report_type} ({category}) - {title}')
            print(f'      Created: {created_at}')
    else:
        print(f'❌ No existing reports found in database')
    
    # Check report generation components
    print(f'\n🔍 CHECKING REPORT GENERATION COMPONENTS:')
    
    # Check if interactive_pre_reporting.js exists
    ui_file = 'ui/interactive_pre_reporting.js'
    if os.path.exists(ui_file):
        print(f'   ✅ Interactive Pre-Reporting UI found: {ui_file}')
        
        # Check file size to ensure it's not empty
        file_size = os.path.getsize(ui_file)
        print(f'      File size: {file_size:,} bytes')
        
        if file_size > 10000:  # Reasonable size for a functional file
            print(f'      ✅ File appears to be complete')
        else:
            print(f'      ⚠️ File may be incomplete or empty')
    else:
        print(f'   ❌ Interactive Pre-Reporting UI not found')
    
    # Check smart report generator
    smart_report_file = 'ui/smart_report_generator.js'
    if os.path.exists(smart_report_file):
        print(f'   ✅ Smart Report Generator found: {smart_report_file}')
    else:
        print(f'   ❌ Smart Report Generator not found')
    
    # Check report distinction manager
    distinction_file = 'ui/report_distinction_manager.js'
    if os.path.exists(distinction_file):
        print(f'   ✅ Report Distinction Manager found: {distinction_file}')
    else:
        print(f'   ❌ Report Distinction Manager not found')
    
    conn.close()
    
    # Test report generation API availability
    print(f'\n🧪 TESTING REPORT GENERATION WORKFLOW:')
    print(f'   📋 Report generation involves:')
    print(f'      1. Interactive Pre-Reporting UI collects user selections')
    print(f'      2. Smart Report Generator processes data with business rules')
    print(f'      3. Report Manager saves generated reports to database')
    print(f'      4. Multiple output formats (Word, PDF, Excel) are supported')
    
    return True

def test_department_fix():
    """Test a fix for department population"""
    print(f'\n🔧 TESTING DEPARTMENT POPULATION FIX')
    print("=" * 60)
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Get latest session
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    if not session_result:
        print('❌ No audit sessions found')
        return False
    
    session_id = session_result[0]
    
    # Function to get department from extracted_data
    def get_reliable_department(employee_id):
        # Try PERSONAL DETAILS first
        cursor.execute('''
            SELECT item_value 
            FROM extracted_data 
            WHERE session_id = ? AND employee_id = ? 
            AND section_name = 'PERSONAL DETAILS' 
            AND (item_label = 'DEPARTMENT' OR item_label = 'Department')
            AND period_type = 'current'
        ''', (session_id, employee_id))
        
        result = cursor.fetchone()
        if result and result[0] and result[0].strip():
            return result[0].strip()
        
        # Try previous period
        cursor.execute('''
            SELECT item_value 
            FROM extracted_data 
            WHERE session_id = ? AND employee_id = ? 
            AND section_name = 'PERSONAL DETAILS' 
            AND (item_label = 'DEPARTMENT' OR item_label = 'Department')
            AND period_type = 'previous'
        ''', (session_id, employee_id))
        
        result = cursor.fetchone()
        if result and result[0] and result[0].strip():
            return result[0].strip()
        
        # Fallback to a default
        return 'DEPARTMENT_NOT_EXTRACTED'
    
    # Test with a few tracker records
    cursor.execute('''
        SELECT DISTINCT employee_id 
        FROM tracker_results 
        WHERE session_id = ? AND (department IS NULL OR department = 'Unknown')
        LIMIT 5
    ''', (session_id,))
    
    test_employees = cursor.fetchall()
    
    if test_employees:
        print(f'Testing department fix for {len(test_employees)} employees:')
        
        for (employee_id,) in test_employees:
            reliable_dept = get_reliable_department(employee_id)
            print(f'   {employee_id}: {reliable_dept}')
            
            if reliable_dept != 'DEPARTMENT_NOT_EXTRACTED':
                print(f'      ✅ Reliable department found')
            else:
                print(f'      ❌ No department data available')
        
        return True
    else:
        print(f'✅ No tracker records need department fixes')
        return True

if __name__ == "__main__":
    print("Investigating department population and report generation issues...")
    
    # Investigation 1: Department population
    dept_success = investigate_department_population()
    
    # Investigation 2: Report generation
    report_success = investigate_report_generation()
    
    # Test 3: Department fix
    fix_success = test_department_fix()
    
    print(f'\n🎯 INVESTIGATION RESULTS:')
    print(f'   Department population: {"✅ SOURCE FOUND" if dept_success else "❌ NO RELIABLE SOURCE"}')
    print(f'   Report generation: {"✅ COMPONENTS FOUND" if report_success else "❌ MISSING COMPONENTS"}')
    print(f'   Department fix test: {"✅ WORKING" if fix_success else "❌ FAILED"}')
    
    print(f'\n💡 RECOMMENDATIONS:')
    if not dept_success:
        print(f'   - Check extraction process to ensure department data is captured')
        print(f'   - Verify PERSONAL DETAILS section includes DEPARTMENT field')
        print(f'   - Consider alternative department data sources')
    
    if not report_success:
        print(f'   - Verify report generation UI components are properly loaded')
        print(f'   - Check Report Manager database schema and connectivity')
        print(f'   - Test report generation workflow end-to-end')
    
    if dept_success and report_success and fix_success:
        print(f'\n🎉 BOTH SYSTEMS APPEAR TO BE FUNCTIONAL!')
        print(f'   - Department data sources are available')
        print(f'   - Report generation components are in place')
        print(f'   - Ready for comprehensive testing')
