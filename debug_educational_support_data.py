#!/usr/bin/env python3
"""
Debug Educational Support Data - Check the actual data in extracted_data table
"""

import sqlite3

def debug_educational_support_data():
    print("🔍 DEBUGGING EDUCATIONAL SUPPORT DATA IN DATABASE")
    print("=" * 60)
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Get latest session
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    if not session_result:
        print('❌ No audit sessions found')
        return
    
    session_id = session_result[0]
    print(f'📅 Using session: {session_id}')
    
    # Check educational support items in extracted_data
    print(f'\n🔍 CHECKING EDUCATIONAL SUPPORT IN EXTRACTED_DATA:')
    cursor.execute('''
        SELECT employee_id, employee_name, section_name, item_label, item_value, period_type
        FROM extracted_data 
        WHERE session_id = ? AND item_label LIKE '%EDUCATIONAL%'
        ORDER BY employee_id, period_type
        LIMIT 20
    ''', (session_id,))
    
    educational_data = cursor.fetchall()
    
    if educational_data:
        print(f'Found {len(educational_data)} educational support records:')
        
        for emp_id, emp_name, section, item, value, period in educational_data:
            print(f'\n👤 {emp_id}: {emp_name}')
            print(f'   {period}: {section}.{item} = "{value}"')
            
            # Check if the value looks wrong
            if 'FUEL' in str(value).upper() or 'ELEMENT' in str(value).upper():
                print(f'   🚨 ISSUE: Educational support has fuel/element value!')
            elif not str(value).replace(',', '').replace('.', '').isdigit():
                print(f'   🚨 ISSUE: Educational support has non-numeric value!')
            else:
                print(f'   ✅ Value looks correct')
    else:
        print('❌ No educational support records found in extracted_data')
    
    # Check specific employees that had issues
    print(f'\n🔍 CHECKING SPECIFIC EMPLOYEES WITH EDUCATIONAL ISSUES:')
    problem_employees = ['COP0555', 'COP1079', 'COP1169', 'COP1335', 'COP1365']
    
    for emp_id in problem_employees:
        print(f'\n👤 {emp_id}:')
        
        # Check current period
        cursor.execute('''
            SELECT section_name, item_label, item_value
            FROM extracted_data 
            WHERE session_id = ? AND employee_id = ? AND period_type = 'current'
            AND (item_label LIKE '%EDUCATIONAL%' OR item_label LIKE '%LEAVE%' OR item_label LIKE '%FUEL%')
            ORDER BY section_name, item_label
        ''', (session_id, emp_id))
        
        current_items = cursor.fetchall()
        
        if current_items:
            print(f'   CURRENT PERIOD:')
            for section, item, value in current_items:
                print(f'     {section}.{item} = "{value}"')
        
        # Check previous period
        cursor.execute('''
            SELECT section_name, item_label, item_value
            FROM extracted_data 
            WHERE session_id = ? AND employee_id = ? AND period_type = 'previous'
            AND (item_label LIKE '%EDUCATIONAL%' OR item_label LIKE '%LEAVE%' OR item_label LIKE '%FUEL%')
            ORDER BY section_name, item_label
        ''', (session_id, emp_id))
        
        previous_items = cursor.fetchall()
        
        if previous_items:
            print(f'   PREVIOUS PERIOD:')
            for section, item, value in previous_items:
                print(f'     {section}.{item} = "{value}"')
        
        if not current_items and not previous_items:
            print(f'   ❌ No educational/leave/fuel items found')
    
    # Check the comparison results for these employees
    print(f'\n🔍 CHECKING COMPARISON RESULTS FOR PROBLEM EMPLOYEES:')
    
    for emp_id in problem_employees[:3]:  # Check first 3
        print(f'\n👤 {emp_id} COMPARISON RESULTS:')
        
        cursor.execute('''
            SELECT section_name, item_label, previous_value, current_value, change_type
            FROM comparison_results 
            WHERE session_id = ? AND employee_id = ?
            AND (item_label LIKE '%EDUCATIONAL%' OR item_label LIKE '%LEAVE%')
            ORDER BY section_name, item_label
        ''', (session_id, emp_id))
        
        comparison_items = cursor.fetchall()
        
        if comparison_items:
            for section, item, prev_val, curr_val, change_type in comparison_items:
                print(f'   {section}.{item}:')
                print(f'     Previous: "{prev_val}"')
                print(f'     Current: "{curr_val}"')
                print(f'     Change: {change_type}')
                
                # Analyze the issue
                if 'EDUCATIONAL' in item and ('FUEL' in str(curr_val) or 'ELEMENT' in str(curr_val)):
                    print(f'     🚨 CONFIRMED ISSUE: Educational item has fuel/element current value!')
                elif 'LEAVE' in item and 'EDUCATIONAL' in str(curr_val):
                    print(f'     🚨 CONFIRMED ISSUE: Leave allowance has educational current value!')
        else:
            print(f'   ❌ No educational/leave comparison results found')
    
    conn.close()

if __name__ == "__main__":
    debug_educational_support_data()
