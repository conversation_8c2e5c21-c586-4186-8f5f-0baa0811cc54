#!/usr/bin/env python3
"""
Check Table Schemas - Compare extracted_data vs extracted_items
"""

import sqlite3

def check_schemas():
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()

    session_id = 'audit_session_1751969578_07067c76'

    print('=== EXTRACTED_DATA TABLE SCHEMA ===')
    cursor.execute("PRAGMA table_info(extracted_data)")
    columns = cursor.fetchall()
    for col in columns:
        print(f'  {col[1]} ({col[2]})')
    
    cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session_id,))
    count = cursor.fetchone()[0]
    print(f'Total records: {count}')

    print('\n=== EXTRACTED_ITEMS TABLE SCHEMA ===')
    cursor.execute("PRAGMA table_info(extracted_items)")
    columns = cursor.fetchall()
    for col in columns:
        print(f'  {col[1]} ({col[2]})')
    
    cursor.execute('SELECT COUNT(*) FROM extracted_items WHERE session_id = ?', (session_id,))
    count = cursor.fetchone()[0]
    print(f'Total records: {count}')

    if count == 0:
        print('\n=== SAMPLE EXTRACTED_DATA RECORDS ===')
        cursor.execute('SELECT employee_id, section_name, item_label, item_value FROM extracted_data WHERE session_id = ? LIMIT 10', (session_id,))
        items = cursor.fetchall()
        for emp_id, section, item, value in items:
            print(f'  {emp_id}: {section} - {item} = {value}')
        
        print('\n=== CHECK FOR DUPLICATES IN EXTRACTED_DATA ===')
        cursor.execute('''
            SELECT employee_id, section_name, item_label, COUNT(*) as count
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY employee_id, section_name, item_label
            HAVING COUNT(*) > 1
            LIMIT 10
        ''', (session_id,))
        duplicates = cursor.fetchall()
        if duplicates:
            print('Found duplicates in extracted_data:')
            for emp_id, section, item, count in duplicates:
                print(f'  {emp_id}: {section} - {item} appears {count} times')
        else:
            print('No duplicates found in extracted_data')

    conn.close()

if __name__ == "__main__":
    check_schemas()
