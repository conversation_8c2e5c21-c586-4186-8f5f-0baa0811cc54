#!/usr/bin/env python3
"""
DUPLICATE CHECKER MODULE
Detects duplicate allowances/loans based on "once per year" business rule
"""

import sqlite3
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Import the new duplicate item configuration
try:
    from core.duplicate_item_config import duplicate_config, is_permissive_duplicate, should_flag_duplicate_anomaly, get_duplicate_anomaly_info
except ImportError:
    # Fallback if config not available
    def is_permissive_duplicate(section_name: str, item_label: str) -> bool:
        return False
    def should_flag_duplicate_anomaly(section_name: str, item_label: str, count: int) -> bool:
        return count > 1
    def get_duplicate_anomaly_info(section_name: str, item_label: str, count: int, amounts: list = None) -> dict:
        return {'is_anomaly': count > 1, 'severity': 'MODERATE', 'description': f'Item appears {count} times', 'is_permissive': False}

class DuplicateChecker:
    """
    Handles duplicate detection for Bank Adviser tracker items
    Business Rule: Each employee can receive each allowance/loan type only ONCE per calendar year
    """

    def __init__(self, debug: bool = False):
        self.debug = debug

        # Define which tables to monitor for duplicates
        self.monitored_tables = {
            'in_house_loans': {
                'type_column': 'loan_type',
                'amount_column': 'loan_amount',
                'claim_type_prefix': 'IN-HOUSE LOAN'
            },
            'leave_claims': {
                'type_column': 'allowance_type',
                'amount_column': 'payable_amount',
                'claim_type_prefix': 'LEAVE CLAIM'
            },
            'educational_subsidy': {
                'type_column': 'allowance_type',
                'amount_column': 'payable_amount',
                'claim_type_prefix': 'EDUCATIONAL SUBSIDY'
            },
            'motor_vehicle_maintenance': {
                'type_column': 'allowance_type',
                'amount_column': 'payable_amount',
                'claim_type_prefix': 'MOTOR VEHICLE MAINTENANCE'
            },
            'long_service_awards': {
                'type_column': 'award_type',
                'amount_column': 'payable_amount',
                'claim_type_prefix': 'LONG SERVICE AWARD'
            }
        }

        if self.debug:
            print("[DUPLICATE-CHECKER] Duplicate Checker initialized")
            print(f"   Monitoring {len(self.monitored_tables)} tracker tables")

    def check_for_duplicate(self, employee_no: str, claim_type: str, year: int,
                          source_table: str, new_record_id: int) -> bool:
        """
        Check if employee already received this claim type in the given year

        Args:
            employee_no: Employee identifier
            claim_type: Type of claim/allowance/loan
            year: Calendar year to check
            source_table: Source table name
            new_record_id: ID of the new record being checked

        Returns:
            True if duplicate detected, False otherwise
        """
        try:
            from core.python_database_manager import get_database_instance

            db = get_database_instance()

            if source_table not in self.monitored_tables:
                if self.debug:
                    print(f"   [DUPLICATE-CHECKER] Table {source_table} not monitored")
                return False

            table_config = self.monitored_tables[source_table]
            type_column = table_config['type_column']

            # Check for existing records in the same year
            existing_records = db.execute_query(
                f"""SELECT id, {type_column}, period_year, period_month, {table_config['amount_column']}
                    FROM {source_table}
                    WHERE employee_no = ? AND {type_column} = ? AND period_year = ? AND id != ?
                    ORDER BY id ASC""",
                (employee_no, claim_type, str(year), new_record_id)
            )

            if existing_records:
                # Duplicate detected - record it
                first_record = existing_records[0]
                self._record_duplicate(
                    employee_no=employee_no,
                    claim_type=claim_type,
                    year=year,
                    source_table=source_table,
                    first_record_id=first_record['id'],
                    second_record_id=new_record_id,
                    first_period=f"{first_record['period_year']}-{first_record['period_month']}",
                    first_amount=first_record[table_config['amount_column']]
                )

                if self.debug:
                    print(f"   [DUPLICATE-CHECKER] DUPLICATE DETECTED: {employee_no} - {claim_type} ({year})")

                return True

            return False

        except Exception as e:
            if self.debug:
                print(f"   [DUPLICATE-CHECKER] Error checking duplicate: {e}")
            return False

    def _record_duplicate(self, employee_no: str, claim_type: str, year: int,
                         source_table: str, first_record_id: int, second_record_id: int,
                         first_period: str, first_amount: float) -> None:
        """Record duplicate in duplicate_checker table"""
        try:
            from core.python_database_manager import get_database_instance

            db = get_database_instance()

            # Get details of the second (duplicate) record
            table_config = self.monitored_tables[source_table]

            second_record = db.execute_query(
                f"""SELECT employee_name, department, period_year, period_month, {table_config['amount_column']}
                    FROM {source_table} WHERE id = ?""",
                (second_record_id,)
            )

            if not second_record:
                return

            record = second_record[0]
            employee_name = record['employee_name']
            department = record['department']
            period_year = record['period_year']
            period_month = record['period_month']
            payable_amount = record[table_config['amount_column']]
            second_period = f"{period_year}-{period_month}"

            # Format claim type with prefix
            formatted_claim_type = f"{table_config['claim_type_prefix']}: {claim_type}"

            # Insert into duplicate_checker table
            db.execute_update(
                """INSERT INTO duplicate_checker
                   (employee_no, employee_name, department, claim_type, payable_amount,
                    second_period, first_period, first_amount, remarks, source_table,
                    first_record_id, second_record_id, year_detected)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    employee_no,
                    employee_name,
                    department,
                    formatted_claim_type,
                    payable_amount,
                    second_period,
                    first_period,
                    first_amount,
                    'TRUE DUPLICATE',
                    source_table,
                    first_record_id,
                    second_record_id,
                    year
                )
            )

            if self.debug:
                print(f"   [DUPLICATE-CHECKER] RECORDED: {formatted_claim_type} for {employee_no}")

        except Exception as e:
            if self.debug:
                print(f"   [DUPLICATE-CHECKER] Error recording duplicate: {e}")

    def scan_all_tables_for_duplicates(self, year: int = None) -> Dict:
        """
        Scan all tracker tables for duplicates in a given year

        Args:
            year: Year to scan (current year if None)

        Returns:
            Dictionary with duplicate statistics
        """
        if year is None:
            year = datetime.now().year

        try:
            try:
                from core.unified_database import UnifiedDatabase
            except ImportError:
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from core.unified_database import UnifiedDatabase

            db = UnifiedDatabase()

            duplicates_found = {
                'total': 0,
                'by_table': {},
                'by_employee': {}
            }

            for table_name, table_config in self.monitored_tables.items():
                type_column = table_config['type_column']
                amount_column = table_config['amount_column']

                # Find duplicates in this table
                duplicate_query = f"""
                    SELECT employee_no, employee_name, department, {type_column},
                           COUNT(*) as occurrence_count,
                           GROUP_CONCAT(period_month || '-' || period_year) as periods,
                           GROUP_CONCAT({amount_column}) as amounts,
                           GROUP_CONCAT(id) as record_ids
                    FROM {table_name}
                    WHERE period_year = ?
                    GROUP BY employee_no, {type_column}
                    HAVING COUNT(*) > 1
                    ORDER BY employee_no, {type_column}
                """

                duplicates = db.getAllQuery(duplicate_query, [str(year)])

                if duplicates:
                    duplicates_found['by_table'][table_name] = len(duplicates)

                    for duplicate in duplicates:
                        emp_no, emp_name, dept, claim_type, count, periods, amounts, record_ids = duplicate

                        if emp_no not in duplicates_found['by_employee']:
                            duplicates_found['by_employee'][emp_no] = []

                        duplicates_found['by_employee'][emp_no].append({
                            'table': table_name,
                            'claim_type': claim_type,
                            'count': count,
                            'periods': periods.split(','),
                            'amounts': [float(a) for a in amounts.split(',')],
                            'record_ids': [int(r) for r in record_ids.split(',')]
                        })

                        duplicates_found['total'] += 1

                        if self.debug:
                            print(f"   [DUPLICATE-CHECKER] Found: {emp_no} - {claim_type} ({count} times)")

            return duplicates_found

        except Exception as e:
            if self.debug:
                print(f"   [DUPLICATE-CHECKER] Error scanning for duplicates: {e}")
            return {'total': 0, 'by_table': {}, 'by_employee': {}}

    def get_duplicate_summary(self) -> List[Dict]:
        """Get summary of all recorded duplicates"""
        try:
            from core.python_database_manager import get_database_instance

            db = get_database_instance()

            duplicates = db.execute_query(
                """SELECT employee_no, employee_name, department, claim_type,
                          payable_amount, second_period, remarks, year_detected,
                          detection_date
                   FROM duplicate_checker
                   ORDER BY detection_date DESC"""
            )

            summary = []
            for dup in duplicates:
                summary.append({
                    'employee_no': dup['employee_no'],
                    'employee_name': dup['employee_name'],
                    'department': dup['department'],
                    'claim_type': dup['claim_type'],
                    'payable_amount': dup['payable_amount'],
                    'second_period': dup['second_period'],
                    'remarks': dup['remarks'],
                    'year_detected': dup['year_detected'],
                    'detection_date': dup['detection_date']
                })

            return summary

        except Exception as e:
            if self.debug:
                print(f"   [DUPLICATE-CHECKER] Error getting duplicate summary: {e}")
            return []
