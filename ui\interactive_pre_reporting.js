/**
 * Interactive Pre-reporting UI Component
 * Displays categorized changes with bulk size analysis and user selection controls
 */

class InteractivePreReporting {
    constructor(container, analyzedChanges = []) {
        // PRODUCTION FIX: Ensure container is always a DOM element
        if (typeof container === 'string') {
            this.container = document.getElementById(container);
            if (!this.container) {
                console.error(`❌ Container element with ID '${container}' not found`);
                throw new Error(`Container element with ID '${container}' not found`);
            }
        } else if (container && container.nodeType === Node.ELEMENT_NODE) {
            this.container = container;
        } else {
            console.error('❌ Invalid container provided:', container);
            throw new Error('Container must be a DOM element or valid element ID string');
        }

        this.analyzedChanges = analyzedChanges;
        this.selectedChanges = new Set();
        this.isLoading = false; // CRITICAL FIX: Prevent infinite loops
        this.bulkCategories = {
            'INDIVIDUAL': { min: 1, max: 3, label: 'Individual Anomalies' },
            'SMALL_BULK': { min: 4, max: 16, label: 'Small Bulk Changes' },
            'MEDIUM_BULK': { min: 17, max: 32, label: 'Medium Bulk Changes' },
            'LARGE_BULK': { min: 33, max: 999, label: 'Large Bulk Changes' }
        };
        this.priorityConfig = {
            sections: {
                'Personal Details': 'HIGH',
                'Earnings': 'HIGH',
                'Deductions': 'HIGH',
                'Bank Details': 'HIGH',
                'Loans': 'MODERATE',
                'Employer Contributions': 'LOW'
            }
        };

        // ENHANCEMENT: Initialize Business Rules Engine and Smart Report Generator
        this.businessRulesEngine = null; // Will be initialized when needed
        this.smartReportGenerator = null; // Will be initialized when needed

        // ENHANCEMENT: Final Report Configuration
        this.finalReportConfig = {
            generatedBy: '',
            designation: '',
            reportType: 'employee-based', // 'employee-based' or 'item-based'
            outputFormat: 'word', // 'word', 'pdf', 'excel'
            businessRules: {
                includePromotions: true,
                includeTransfers: true,
                groupByDepartment: false,
                autoGenerateAppendix: true
            }
        };

        // ROBUSTNESS: Interface stability and loading management
        this.isProcessing = false;
        this.loadingStates = new Map();
        this.debounceTimers = new Map();
        this.retryAttempts = new Map();
        this.maxRetries = 3;
        this.currentSortBy = 'category'; // Track current sort for persistence
        this.expandedDetails = new Set(); // Track which change details are expanded

        // PRODUCTION FIX: Search state management
        this.currentSearchTerm = '';
        this.preSearchState = null; // Store state before search for restoration
        this.searchDebounceTimer = null; // Debounce search for responsiveness
    }

    async initialize() {
        console.log('📋 Initializing Final Reporting with', this.analyzedChanges.length, 'changes');

        if (!this.container || !this.container.nodeType) {
            console.error('❌ Invalid or missing container for final report UI');
            console.error('Container:', this.container);
            throw new Error('Invalid or missing container for final report UI');
        }

        // PRODUCTION FIX: Expose global functions immediately to prevent onclick handler errors
        this.exposeGlobalFunctions();

        // ENHANCEMENT: Load saved report configuration
        this.loadReportConfig();

        // UPDATED: Check new API availability for direct comparison results
        console.log('🔍 DEBUG: window.api available:', !!window.api);
        console.log('🔍 DEBUG: getComparisonResults available:', !!(window.api && window.api.getComparisonResults));
        console.log('🔍 DEBUG: getLatestComparisonResults available:', !!(window.api && window.api.getLatestComparisonResults));
        console.log('🔍 DEBUG: getPreReportingData (deprecated) available:', !!(window.api && window.api.getPreReportingData));

        // Load data from database if not provided
        if (!this.analyzedChanges || this.analyzedChanges.length === 0) {
            console.log('📊 No data provided, loading from database...');
            await this.loadDataFromDatabase();
        } else {
            console.log('📊 Using provided data, processing and rendering...');
            this.processAndRender();
        }
    }

    async loadDataFromDatabase() {
        try {
            console.log('📊 Loading pre-reporting data from database...');

            // CRITICAL FIX: Prevent multiple loading attempts
            if (this.isLoading) {
                console.log('⚠️ Already loading data, skipping duplicate request');
                return;
            }
            this.isLoading = true;

            // UPDATED: Check new API availability first
            if (!window.api) {
                throw new Error('window.api is not available - preload script may not be loaded');
            }

            if (!window.api.getComparisonResults && !window.api.getLatestComparisonResults) {
                throw new Error('Interactive Reporting API methods not available');
            }

            // CRITICAL FIX: Get current session ID first
            let sessionId = null;

            // Method 1: Try to get current session from global variable
            if (window.currentSessionId) {
                sessionId = window.currentSessionId;
                console.log('✅ Using global session ID:', sessionId);
            }

            // Method 2: Try to get from unified session manager
            if (!sessionId && window.api.getCurrentSessionId) {
                try {
                    const sessionResult = await window.api.getCurrentSessionId();
                    if (sessionResult && sessionResult.success) {
                        sessionId = sessionResult.session_id;
                        console.log('✅ Using unified session ID:', sessionId);
                    }
                } catch (sessionError) {
                    console.log('⚠️ Unified session manager not available:', sessionError.message);
                }
            }

            // UPDATED: Load comparison results directly (no more PRE_REPORTING dependency)
            let response = null;

            if (sessionId) {
                console.log(`📊 Loading comparison results for session: ${sessionId}`);
                response = await window.api.getComparisonResults(sessionId);
            } else {
                console.log('⚠️ No specific session ID, using latest comparison results');
                response = await window.api.getLatestComparisonResults();
            }

            // CRITICAL FIX: More flexible data validation
            let dataLoaded = false;

            if (response && response.success) {
                // Handle different response formats
                if (response.data && Array.isArray(response.data)) {
                    this.analyzedChanges = response.data;
                    dataLoaded = true;
                } else if (response.data && response.data.length !== undefined) {
                    this.analyzedChanges = response.data;
                    dataLoaded = true;
                } else if (Array.isArray(response)) {
                    // Direct array response
                    this.analyzedChanges = response;
                    dataLoaded = true;
                } else if (response.changes && Array.isArray(response.changes)) {
                    // Changes in different property
                    this.analyzedChanges = response.changes;
                    dataLoaded = true;
                }
            } else if (response && Array.isArray(response)) {
                // Direct array response without success wrapper
                this.analyzedChanges = response;
                dataLoaded = true;
            }

            if (dataLoaded) {
                console.log('✅ Loaded', this.analyzedChanges.length, 'comparison results for Interactive UI');
                console.log('📊 Session used:', response.session_id || sessionId || 'latest');

                // CRITICAL DEBUG: Check data size and structure
                console.log('🔍 DEBUG: Data size:', this.analyzedChanges.length);
                console.log('🔍 DEBUG: Sample data:', this.analyzedChanges.slice(0, 2));

                // PERFORMANCE FIX: Handle large datasets gracefully
                if (this.analyzedChanges.length > 10000) {
                    console.log('⚠️ Large dataset detected, using performance mode');
                    this.showLoadingState('Processing large dataset...');

                    // Use setTimeout to prevent UI blocking
                    setTimeout(() => {
                        this.processAndRender();
                    }, 100);
                } else {
                    // FIXED: Single call to processAndRender to prevent infinite loops
                    this.processAndRender();
                }
            } else {
                // CRITICAL FIX: Only show error if data truly unavailable, but still try to render UI
                console.warn('⚠️ No comparison results available, but continuing with UI load:', response);
                this.analyzedChanges = []; // Initialize empty array
                this.processAndRender(); // Still render UI even with no data
            }
        } catch (error) {
            console.error('❌ Error loading comparison results for Interactive UI:', error);
            this.showError('Failed to load comparison results: ' + error.message);
        } finally {
            // CRITICAL FIX: Reset loading flag
            this.isLoading = false;
        }
    }

    processAndRender() {
        try {
            console.log('🔄 Starting processAndRender with', this.analyzedChanges.length, 'changes');

            // Categorize changes by bulk size and priority
            console.log('📊 Categorizing changes...');
            const categorizedData = this.categorizeChanges();
            console.log('✅ Categorization complete');

            // Apply auto-selection rules
            console.log('🎯 Applying auto-selection rules...');
            this.applyAutoSelection(categorizedData);
            console.log('✅ Auto-selection complete');

            // CRITICAL FIX: Use chunked rendering for large datasets to prevent UI freeze
            if (this.analyzedChanges.length > 1000) {
                console.log('🎨 Starting chunked rendering for large dataset...');
                this.renderChunked(categorizedData);
            } else {
                console.log('🎨 Starting direct rendering for small dataset...');
                this.render(categorizedData);
            }

        } catch (error) {
            console.error('❌ Error in processAndRender:', error);
            this.showError('Failed to process and render data: ' + error.message);
        }
    }

    async renderChunked(categorizedData) {
        console.log('🔄 Starting optimized chunked rendering to prevent UI freeze...');

        // Show loading state
        this.showLoadingState('Rendering changes...');

        try {
            // PERFORMANCE OPTIMIZATION: Render categories one by one, with limited items per category
            const totalChanges = Object.values(categorizedData).reduce((sum, changes) => sum + changes.length, 0);
            const selectedCount = Array.from(this.selectedChanges).length;

            // Build the main structure first (fast)
            if (!this.container || !this.container.nodeType) {
                console.error('❌ Cannot render chunked - invalid container:', this.container);
                return;
            }
            this.container.innerHTML = `
                <div class="final-report-interface">
                    <div class="final-report-header">
                        <h3>📋 FINAL REPORTING</h3>
                        <p class="interface-subtitle">Interactive Change Review & Advanced Report Generation</p>

                        <!-- Shared Report Configuration Panel -->
                        ${this.renderSharedConfigPanel()}

                        <div class="summary-stats">
                            <span class="stat-item">
                                <strong>${totalChanges}</strong> Total Changes
                            </span>
                            <span class="stat-item">
                                <strong>${selectedCount}</strong> Auto-Selected
                            </span>
                            <span class="stat-item">
                                <strong>${totalChanges - selectedCount}</strong> Pending Review
                            </span>
                        </div>
                    </div>

                    <!-- Persistent Sort Controls -->
                    ${this.renderPersistentSortControls()}

                    <div class="bulk-categories" id="bulk-categories-container">
                        <div class="loading-message">Loading categories...</div>
                    </div>
                </div>
            `;

            // Now render categories one by one with delays
            const categoriesContainer = document.getElementById('bulk-categories-container');
            categoriesContainer.innerHTML = '';

            let categoryIndex = 0;
            for (const [category, changes] of Object.entries(categorizedData)) {
                await new Promise(resolve => {
                    setTimeout(() => {
                        const categoryHTML = this.renderBulkCategory(category, changes);
                        categoriesContainer.innerHTML += categoryHTML;

                        // Update progress
                        const progress = ((categoryIndex + 1) / Object.keys(categorizedData).length) * 100;
                        this.updateLoadingProgress(progress);

                        categoryIndex++;
                        resolve();
                    }, 20); // Small delay between categories
                });
            }

            // Attach event listeners after all rendering is complete
            this.attachEventListeners();

            // Hide loading state
            this.hideLoadingState();

            console.log('✅ Optimized chunked rendering completed');
        } catch (error) {
            console.error('❌ Chunked rendering failed:', error);
            this.hideLoadingState();
            this.showError('Failed to render changes');
        }
    }

    categorizeChanges() {
        console.log('📊 Categorizing changes by bulk size and priority...');
        
        // Group changes by item type to determine bulk size
        const itemGroups = {};
        
        this.analyzedChanges.forEach(change => {
            const key = `${change.section_name}::${change.item_label}::${change.change_type}`;

            if (!itemGroups[key]) {
                itemGroups[key] = {
                    changes: [],
                    section: change.section_name,
                    item: change.item_label,
                    changeType: change.change_type,
                    priority: this.determinePriority(change.section_name)
                };
            }

            itemGroups[key].changes.push(change);
        });

        // Categorize by bulk size
        const categorized = {
            'INDIVIDUAL': [],
            'SMALL_BULK': [],
            'MEDIUM_BULK': [],
            'LARGE_BULK': []
        };

        Object.values(itemGroups).forEach(group => {
            const employeeCount = group.changes.length;
            const category = this.getBulkCategory(employeeCount);
            
            group.changes.forEach(change => {
                change.bulk_category = category;
                change.bulk_size = employeeCount;
                change.priority = group.priority;
            });
            
            categorized[category].push(...group.changes);
        });

        return categorized;
    }

    determinePriority(sectionName) {
        const normalizedSection = sectionName.toLowerCase();
        
        if (normalizedSection.includes('personal') || normalizedSection.includes('earnings') || 
            normalizedSection.includes('deductions') || normalizedSection.includes('bank')) {
            return 'HIGH';
        } else if (normalizedSection.includes('loan')) {
            return 'MODERATE';
        } else {
            return 'LOW';
        }
    }

    getBulkCategory(employeeCount) {
        if (employeeCount <= 3) return 'INDIVIDUAL';
        if (employeeCount <= 16) return 'SMALL_BULK';
        if (employeeCount <= 32) return 'MEDIUM_BULK';
        return 'LARGE_BULK';
    }

    applyAutoSelection(categorizedData) {
        console.log('🚀 Applying auto-selection rules...');
        
        // Auto-select rules:
        // 1. All Individual Anomalies (HIGH/MODERATE priority)
        // 2. Small Bulk changes (HIGH priority only)
        // 3. Medium Bulk changes (HIGH priority only)
        // 4. Large Bulk changes (manual selection required)
        
        Object.entries(categorizedData).forEach(([category, changes]) => {
            changes.forEach(change => {
                const shouldAutoSelect = this.shouldAutoSelect(category, change.priority);
                
                if (shouldAutoSelect) {
                    this.selectedChanges.add(change.id);
                }
            });
        });
        
        console.log('✅ Auto-selected', this.selectedChanges.size, 'changes');
    }

    shouldAutoSelect(category, priority) {
        if (category === 'INDIVIDUAL') {
            return priority === 'HIGH' || priority === 'MODERATE';
        } else if (category === 'SMALL_BULK' || category === 'MEDIUM_BULK') {
            return priority === 'HIGH';
        } else if (category === 'LARGE_BULK') {
            return false; // Always require manual selection
        }
        return false;
    }

    render(categorizedData) {
        console.log('🎨 Rendering pre-reporting interface...');

        if (!this.container || !this.container.nodeType) {
            console.error('❌ Cannot render - invalid container:', this.container);
            return;
        }

        const totalChanges = Object.values(categorizedData).flat().length;
        const selectedCount = this.selectedChanges.size;

        this.container.innerHTML = `
            <div class="final-report-interface">
                <div class="final-report-header">
                    <h3>📋 FINAL REPORTING</h3>
                    <p class="interface-subtitle">Interactive Change Review & Advanced Report Generation</p>

                    <!-- Shared Report Configuration Panel -->
                    ${this.renderSharedConfigPanel()}

                    <div class="summary-stats">
                        <span class="stat-item">
                            <strong>${totalChanges}</strong> Total Changes
                        </span>
                        <span class="stat-item">
                            <strong>${selectedCount}</strong> Auto-Selected
                        </span>
                        <span class="stat-item">
                            <strong>${totalChanges - selectedCount}</strong> Pending Review
                        </span>
                    </div>
                </div>

                <div class="bulk-categories">
                    ${Object.entries(categorizedData).map(([category, changes]) => 
                        this.renderBulkCategory(category, changes)
                    ).join('')}
                </div>

                <!-- Persistent Sort Controls -->
                ${this.renderPersistentSortControls()}
            </div>
        `;

        // Add event listeners for individual change selection
        this.attachEventListeners();
    }

    renderBulkCategory(category, changes) {
        if (changes.length === 0) return '';
        
        const categoryInfo = this.bulkCategories[category];
        const selectedInCategory = changes.filter(c => this.selectedChanges.has(c.id)).length;
        
        return `
            <div class="bulk-category" data-category="${category}">
                <div class="category-header">
                    <h4>
                        <i class="fas fa-${this.getCategoryIcon(category)}"></i>
                        ${categoryInfo.label}
                    </h4>
                    <div class="category-stats">
                        <span class="change-count">${changes.length} changes</span>
                        <span class="selected-count">${selectedInCategory} selected</span>
                    </div>
                </div>
                
                <div class="changes-list">
                    ${changes.slice(0, 10).map(change => this.renderChangeItem(change)).join('')}
                    ${changes.length > 10 ? `
                        <div class="more-changes">
                            <button class="btn-link" onclick="window.interactivePreReporting.showAllChanges('${category}')">
                                Show all ${changes.length} changes...
                            </button>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    renderChangeItem(change) {
        const isSelected = this.selectedChanges.has(change.id);
        const isExpanded = this.expandedDetails.has(change.id);
        const priorityClass = change.priority.toLowerCase();

        return `
            <div class="change-item ${isSelected ? 'selected' : ''}" data-change-id="${change.id}">
                <div class="change-checkbox">
                    <input type="checkbox" ${isSelected ? 'checked' : ''}
                           onchange="window.interactivePreReporting.toggleChange(${change.id})">
                </div>
                <div class="change-details">
                    <div class="change-header">
                        <span class="employee-info">${change.employee_name} (${change.employee_id})</span>
                        <span class="priority-badge priority-${priorityClass}">${change.priority}</span>
                        <button class="btn-expand" onclick="window.interactivePreReporting.toggleChangeDetails(${change.id})"
                                title="View detailed information">
                            <i class="fas fa-chevron-${isExpanded ? 'up' : 'down'}"></i>
                        </button>
                    </div>
                    <div class="change-description">
                        <strong>${change.section_name}</strong> - ${change.item_label}
                        <span class="change-type">${change.changeType || change.change_type || 'Dictionary Item'}</span>
                        ${change.event_tag && change.event_tag !== 'OTHER_CHANGE' ? `<span class="event-tag event-tag-${change.event_tag.toLowerCase().replace(/[_+]/g, '-').replace(/\s+/g, '-')}" title="${change.event_summary || ''}">${this.getEventIcon(change.event_tag)} ${change.event_tag.replace(/[_+]/g, ' ')}</span>` : ''}
                        ${change.change_type === 'CONSOLIDATED' ? `<span class="consolidated-indicator" title="Click to expand ${change.sections_affected ? change.sections_affected.length : 'multiple'} detailed changes"><i class="fas fa-layer-group"></i> Consolidated</span>` : ''}
                    </div>
                    <div class="change-values">
                        <span class="confidence">Confidence: ${(change.confidence_score * 100).toFixed(0)}%</span>
                        <span class="status">${change.auto_approved ? 'Auto-Approved' : 'Pending Review'}</span>
                    </div>
                    <div class="change-details-expanded" id="details-${change.id}" style="display: ${isExpanded ? 'block' : 'none'};">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Employee ID:</label>
                                <span>${change.employee_id}</span>
                            </div>
                            <div class="detail-item">
                                <label>Employee Name:</label>
                                <span>${change.employee_name}</span>
                            </div>
                            <div class="detail-item">
                                <label>Section:</label>
                                <span>${change.section_name}</span>
                            </div>
                            <div class="detail-item">
                                <label>Item:</label>
                                <span>${change.item_label}</span>
                            </div>
                            <div class="detail-item">
                                <label>Change Type:</label>
                                <span class="badge ${change.change_type.toLowerCase()}">${change.change_type}</span>
                            </div>
                            <div class="detail-item">
                                <label>Priority:</label>
                                <span class="badge priority-${priorityClass}">${change.priority}</span>
                            </div>
                            ${change.previous_value ? `
                                <div class="detail-item">
                                    <label>Previous Value:</label>
                                    <span>${change.previous_value}</span>
                                </div>
                            ` : ''}
                            ${change.current_value ? `
                                <div class="detail-item">
                                    <label>Current Value:</label>
                                    <span>${change.current_value}</span>
                                </div>
                            ` : ''}
                            ${change.numeric_difference ? `
                                <div class="detail-item">
                                    <label>Numeric Difference:</label>
                                    <span>${change.numeric_difference}</span>
                                </div>
                            ` : ''}
                            ${change.percentage_change ? `
                                <div class="detail-item">
                                    <label>Percentage Change:</label>
                                    <span>${change.percentage_change}%</span>
                                </div>
                            ` : ''}
                            <div class="detail-item">
                                <label>Bulk Category:</label>
                                <span>${change.bulk_category || 'Individual'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Bulk Size:</label>
                                <span>${change.bulk_size || 1} employee(s)</span>
                            </div>
                            ${change.event_tag ? `
                                <div class="detail-item">
                                    <label>Event Type:</label>
                                    <span class="badge event-tag-${change.event_tag.toLowerCase().replace('_', '-')}">${change.event_tag.replace('_', ' ')}</span>
                                </div>
                            ` : ''}
                            ${change.event_summary ? `
                                <div class="detail-item">
                                    <label>Event Summary:</label>
                                    <span>${change.event_summary}</span>
                                </div>
                            ` : ''}
                            ${change.business_impact ? `
                                <div class="detail-item">
                                    <label>Business Impact:</label>
                                    <span>${change.business_impact}</span>
                                </div>
                            ` : ''}
                            ${change.change_type === 'CONSOLIDATED' && change.consolidated_details ? `
                                <div class="detail-item consolidated-details">
                                    <label>Consolidated Changes:</label>
                                    <div class="consolidated-changes-list">
                                        ${this.renderConsolidatedDetails(change.consolidated_details)}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getCategoryIcon(category) {
        const icons = {
            'INDIVIDUAL': 'user',
            'SMALL_BULK': 'users',
            'MEDIUM_BULK': 'user-friends',
            'LARGE_BULK': 'building'
        };
        return icons[category] || 'list';
    }

    getEventIcon(eventTag) {
        const icons = {
            'NEW_EMPLOYEE': '🆕',
            'REMOVED_EMPLOYEE': '🏃‍♂️',
            'STAFF-PROMOTION': '📈',
            'MINISTER-PROMOTION': '🏛️',
            'STAFF-TRANSFER': '🔄',
            'MINISTER-TRANSFER': '🏢',
            'ANNUAL_INCREMENT': '📊',
            // Handle combined events
            'STAFF-PROMOTION + STAFF-TRANSFER': '📈🔄',
            'MINISTER-PROMOTION + MINISTER-TRANSFER': '🏛️🏢',
            'STAFF-TRANSFER + ANNUAL_INCREMENT': '🔄📊',
            'MINISTER-TRANSFER + ANNUAL_INCREMENT': '🏢📊'
        };

        // Handle any combined event not explicitly listed
        if (eventTag && eventTag.includes('+')) {
            const parts = eventTag.split('+').map(p => p.trim());
            return parts.map(part => icons[part] || '📋').join('');
        }

        return icons[eventTag] || '📋';
    }

    renderConsolidatedDetails(consolidatedDetails) {
        try {
            // Parse consolidated details if it's a JSON string
            const details = typeof consolidatedDetails === 'string'
                ? JSON.parse(consolidatedDetails)
                : consolidatedDetails;

            if (!Array.isArray(details)) return '<span>No detailed changes available</span>';

            return details.map(detail => `
                <div class="consolidated-detail-item">
                    <div class="detail-header">
                        <span class="section-name">${detail.section_name}</span>
                        <span class="change-type-badge ${detail.change_type.toLowerCase()}">${detail.change_type}</span>
                    </div>
                    <div class="detail-content">
                        <strong>${detail.item_label}</strong>
                        ${detail.previous_value && detail.current_value ? `
                            <div class="value-change">
                                <span class="previous-value">${detail.previous_value}</span>
                                <i class="fas fa-arrow-right"></i>
                                <span class="current-value">${detail.current_value}</span>
                                ${detail.numeric_difference ? `
                                    <span class="numeric-diff ${detail.numeric_difference > 0 ? 'positive' : 'negative'}">
                                        ${detail.numeric_difference > 0 ? '+' : ''}${detail.numeric_difference}
                                    </span>
                                ` : ''}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        } catch (error) {
            console.error('Error rendering consolidated details:', error);
            return '<span>Error loading detailed changes</span>';
        }
    }

    attachEventListeners() {
        try {
            console.log('🔗 Attaching event listeners...');

            // Handle configuration input changes with event delegation
            this.container.removeEventListener('input', this.handleConfigInput);
            this.container.removeEventListener('change', this.handleConfigChange);

            // Bind the event handlers to maintain 'this' context
            this.handleConfigInput = this.handleConfigInput.bind(this);
            this.handleConfigChange = this.handleConfigChange.bind(this);
            this.handleSortChange = this.handleSortChange.bind(this);

            // Add event listeners for configuration inputs
            this.container.addEventListener('input', this.handleConfigInput);
            this.container.addEventListener('change', this.handleConfigChange);

            // Add event listener for persistent sort dropdown
            const sortDropdown = document.getElementById('persistent-sort-dropdown');
            if (sortDropdown) {
                sortDropdown.removeEventListener('change', this.handleSortChange);
                sortDropdown.addEventListener('change', this.handleSortChange);
                console.log('✅ Sort dropdown listener attached');
            }

            // Add specific listeners for configuration fields to ensure immediate updates
            const configFields = [
                'final-report-generated-by',
                'final-report-designation',
                'final-report-type',
                'final-report-format'
            ];

            configFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.removeEventListener('input', this.handleConfigFieldChange);
                    field.removeEventListener('change', this.handleConfigFieldChange);
                    field.addEventListener('input', this.handleConfigFieldChange.bind(this));
                    field.addEventListener('change', this.handleConfigFieldChange.bind(this));
                }
            });

            console.log('✅ Event listeners attached for configuration inputs and sort controls');

        } catch (error) {
            console.error('❌ Error attaching event listeners:', error);
        }
    }

    // Enhanced handler for configuration field changes
    handleConfigFieldChange(event) {
        try {
            const field = event.target;
            const configField = field.dataset.configField;
            const value = field.value.trim();

            if (configField && this.finalReportConfig) {
                this.finalReportConfig[configField] = value;
                console.log(`🔄 Config updated: ${configField} = "${value}"`);

                // Save to localStorage for persistence
                this.saveReportConfig();

                // Update button states immediately
                this.updateReportButtonStates();
            }

        } catch (error) {
            console.error('❌ Error handling config field change:', error);
        }
    }

    // PRODUCTION FIX: Replace placeholder functions with actual bound methods
    exposeGlobalFunctions() {
        // Replace placeholder functions with actual bound methods
        window.interactivePreReporting.toggleChangeDetails = this.toggleChangeDetails.bind(this);
        window.interactivePreReporting.showAllChanges = this.showAllChanges.bind(this);
        window.interactivePreReporting.toggleChange = this.toggleChange.bind(this);
        window.interactivePreReporting.generateFinalReport = this.generateFinalReport.bind(this);
        window.interactivePreReporting.generatePreReport = this.generatePreReport.bind(this);
        window.interactivePreReporting.togglePriorityGroup = this.togglePriorityGroup.bind(this);
        window.interactivePreReporting.toggleEmployeeGroup = this.toggleEmployeeGroup.bind(this);
        window.interactivePreReporting.toggleFlagGroup = this.toggleFlagGroup.bind(this);
        window.interactivePreReporting.handleSearch = this.handleSearch.bind(this);
        window.interactivePreReporting.handleSearchDebounced = this.handleSearchDebounced.bind(this);
        window.interactivePreReporting.clearSearch = this.clearSearch.bind(this);
        window.interactivePreReporting.loadDataFromDatabase = this.loadDataFromDatabase.bind(this);
        window.interactivePreReporting.closeSmartReportPreview = this.closeSmartReportPreview.bind(this);
        window.interactivePreReporting.generateWordDocument = this.generateWordDocument.bind(this);
        window.interactivePreReporting.generatePDFDocument = this.generatePDFDocument.bind(this);
        window.interactivePreReporting.generateExcelDocument = this.generateExcelDocument.bind(this);
        window.interactivePreReporting.generateAllFormats = this.generateAllFormats.bind(this);
        window.interactivePreReporting.generateFinalSmartReport = this.generateFinalSmartReport.bind(this);
        window.interactivePreReporting.saveCurrentSmartReportToManager = this.saveCurrentSmartReportToManager.bind(this);

        console.log('✅ Placeholder functions replaced with actual bound methods');
    }

    // Handle input events for text fields (real-time updates)
    handleConfigInput(event) {
        if (event.target.classList.contains('config-input')) {
            const field = event.target.getAttribute('data-config-field');
            const value = event.target.value;
            this.updateReportConfig(field, value);
        }
    }

    // Handle change events for select fields
    handleConfigChange(event) {
        if (event.target.classList.contains('config-select')) {
            const field = event.target.getAttribute('data-config-field');
            const value = event.target.value;
            this.updateReportConfig(field, value);
        }
    }

    // Handle persistent sort dropdown changes
    handleSortChange(event) {
        const sortValue = event.target.value;
        console.log('🔄 Persistent sort changed to:', sortValue);

        // Prevent multiple rapid sort changes
        if (this.isProcessing) {
            console.log('⚠️ Sort change ignored - processing in progress');
            return;
        }

        // Store the current sort value
        this.currentSortBy = sortValue;

        // Apply the sorting with robustness
        this.applySortingRobust(sortValue);
    }

    // ROBUSTNESS: Apply sorting with error handling and loading states
    applySortingRobust(sortValue) {
        console.log(`🔄 Applying robust sorting: ${sortValue}`);
        this.setLoadingState('sorting', true);
        this.isProcessing = true;

        try {
            // Store current state before sorting
            const currentSelections = new Set(this.selectedChanges);
            const currentConfig = { ...this.finalReportConfig };
            const currentSort = this.currentSortBy;
            const currentExpandedDetails = new Set(this.expandedDetails);

            console.log('💾 Storing state before sorting:', {
                selections: currentSelections.size,
                config: currentConfig,
                sort: currentSort,
                expandedDetails: currentExpandedDetails.size
            });

            // Apply the sorting without losing the dropdown
            this.applySorting(sortValue);

            // Restore state after re-render with multiple attempts for robustness
            const restoreState = (attempt = 1) => {
                try {
                    console.log(`🔄 Restoring state (attempt ${attempt})...`);

                    // Restore selections and state
                    this.selectedChanges = currentSelections;
                    this.finalReportConfig = currentConfig;
                    this.currentSortBy = sortValue;
                    this.expandedDetails = currentExpandedDetails;

                    // Restore dropdown value
                    const dropdown = document.getElementById('persistent-sort-dropdown');
                    if (dropdown && dropdown.value !== sortValue) {
                        dropdown.value = sortValue;
                    }

                    // Restore config inputs and update UI
                    this.restoreConfigInputs();
                    this.updateSelectionUI();
                    this.restoreExpandedDetails();

                    // Verify restoration was successful
                    const configPanel = document.getElementById('shared-report-configuration');
                    if (!configPanel && attempt < 3) {
                        console.warn(`⚠️ Config panel not found, retrying... (attempt ${attempt})`);
                        setTimeout(() => restoreState(attempt + 1), 50);
                        return;
                    }

                    this.setLoadingState('sorting', false);
                    this.isProcessing = false;

                    console.log('✅ State restoration completed successfully');

                } catch (restoreError) {
                    console.error('❌ Error during state restoration:', restoreError);
                    if (attempt < 3) {
                        setTimeout(() => restoreState(attempt + 1), 100);
                    } else {
                        this.setLoadingState('sorting', false);
                        this.isProcessing = false;
                        this.showErrorMessage('Failed to restore UI state after sorting.');
                    }
                }
            };

            // Start restoration with a small delay to ensure DOM is ready
            setTimeout(() => restoreState(), 100);

        } catch (error) {
            console.error('❌ Error during sorting:', error);
            this.showErrorMessage('Sorting failed. Please try again.');
            this.setLoadingState('sorting', false);
            this.isProcessing = false;
        }
    }

    // ROBUSTNESS: Set loading state for specific operations
    setLoadingState(operation, isLoading) {
        this.loadingStates.set(operation, isLoading);

        // Update UI to show loading state
        const loadingIndicator = document.getElementById(`loading-${operation}`);
        if (loadingIndicator) {
            loadingIndicator.style.display = isLoading ? 'block' : 'none';
        }

        // Disable relevant buttons during loading
        this.updateButtonStates();
    }

    // ROBUSTNESS: Update button states based on loading/processing status
    updateButtonStates() {
        const isAnyLoading = Array.from(this.loadingStates.values()).some(state => state);
        const buttons = this.container.querySelectorAll('button');

        buttons.forEach(button => {
            if (isAnyLoading || this.isProcessing) {
                button.disabled = true;
                button.style.opacity = '0.6';
                button.style.cursor = 'not-allowed';
            } else {
                button.disabled = false;
                button.style.opacity = '1';
                button.style.cursor = 'pointer';
            }
        });
    }

    // ROBUSTNESS: Debounced function execution to prevent rapid clicks
    debounce(key, func, delay = 300) {
        if (this.debounceTimers.has(key)) {
            clearTimeout(this.debounceTimers.get(key));
        }

        const timer = setTimeout(() => {
            func();
            this.debounceTimers.delete(key);
        }, delay);

        this.debounceTimers.set(key, timer);
    }

    // ROBUSTNESS: Retry mechanism for failed operations
    async retryOperation(operationName, operation, maxRetries = this.maxRetries) {
        const currentAttempts = this.retryAttempts.get(operationName) || 0;

        try {
            const result = await operation();
            this.retryAttempts.delete(operationName);
            return result;
        } catch (error) {
            if (currentAttempts < maxRetries) {
                console.warn(`⚠️ ${operationName} failed, retrying... (${currentAttempts + 1}/${maxRetries})`);
                this.retryAttempts.set(operationName, currentAttempts + 1);

                // Exponential backoff
                const delay = Math.pow(2, currentAttempts) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));

                return this.retryOperation(operationName, operation, maxRetries);
            } else {
                console.error(`❌ ${operationName} failed after ${maxRetries} attempts:`, error);
                this.retryAttempts.delete(operationName);
                throw error;
            }
        }
    }

    toggleChange(changeId) {
        if (this.selectedChanges.has(changeId)) {
            this.selectedChanges.delete(changeId);
        } else {
            this.selectedChanges.add(changeId);
        }
        
        // Update UI
        this.updateSelectionUI();
    }

    selectAll() {
        this.analyzedChanges.forEach(change => {
            this.selectedChanges.add(change.id);
        });
        this.updateSelectionUI();
    }

    clearAll() {
        this.selectedChanges.clear();
        this.updateSelectionUI();
    }

    toggleChangeDetails(changeId) {
        const detailsElement = document.getElementById(`details-${changeId}`);
        const expandButton = document.querySelector(`[data-change-id="${changeId}"] .btn-expand i`);

        if (detailsElement) {
            const isVisible = detailsElement.style.display !== 'none';
            detailsElement.style.display = isVisible ? 'none' : 'block';

            // Track expanded state
            if (isVisible) {
                this.expandedDetails.delete(changeId);
            } else {
                this.expandedDetails.add(changeId);
            }

            if (expandButton) {
                expandButton.className = isVisible ? 'fas fa-chevron-down' : 'fas fa-chevron-up';
            }

            console.log(`🔄 Toggled details for change ${changeId}, expanded: ${!isVisible}`);
        }
    }

    async showAllChanges(category) {
        // Find the category container
        const categoryContainer = document.querySelector(`[data-category="${category}"] .changes-list`);
        if (!categoryContainer) return;

        // Get all changes for this category
        const categoryData = this.categorizeChanges();
        const allChanges = categoryData[category] || [];

        console.log(`🔄 Showing all ${allChanges.length} changes for category: ${category}`);

        // PERFORMANCE FIX: Use chunked rendering for large categories
        if (allChanges.length > 100) {
            console.log('⚡ Using chunked rendering for large category');

            // Show loading state
            categoryContainer.innerHTML = '<div class="loading-message">Loading all changes...</div>';

            // Render in chunks to prevent UI freeze
            const CHUNK_SIZE = 50;
            let renderedHTML = '';

            for (let i = 0; i < allChanges.length; i += CHUNK_SIZE) {
                const chunk = allChanges.slice(i, i + CHUNK_SIZE);

                // Process chunk
                await new Promise(resolve => {
                    setTimeout(() => {
                        chunk.forEach(change => {
                            renderedHTML += this.renderChangeItem(change);
                        });

                        // Update container with current progress
                        categoryContainer.innerHTML = renderedHTML +
                            `<div class="loading-message">Loading... ${Math.min(i + CHUNK_SIZE, allChanges.length)}/${allChanges.length}</div>`;

                        resolve();
                    }, 10); // Small delay to prevent UI freeze
                });
            }

            // Final render without loading message
            categoryContainer.innerHTML = renderedHTML;
        } else {
            // Direct render for small categories
            categoryContainer.innerHTML = allChanges.map(change => this.renderChangeItem(change)).join('');
        }

        // Re-attach event listeners
        this.attachEventListeners();
    }

    filterByPriority(priority) {
        console.log(`🔍 Filtering by priority: ${priority}`);

        // Show/hide changes based on priority
        const changeItems = this.container.querySelectorAll('.change-item');
        changeItems.forEach(item => {
            const priorityBadge = item.querySelector('.priority-badge');
            if (priorityBadge) {
                const itemPriority = priorityBadge.textContent.trim();
                if (priority === 'ALL' || itemPriority === priority) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            }
        });
    }

    groupByEmployee() {
        console.log('👥 Grouping changes by employee...');

        // Group changes by employee
        const employeeGroups = {};
        this.analyzedChanges.forEach(change => {
            const empKey = `${change.employee_id}-${change.employee_name}`;
            if (!employeeGroups[empKey]) {
                employeeGroups[empKey] = {
                    employee_id: change.employee_id,
                    employee_name: change.employee_name,
                    changes: []
                };
            }
            employeeGroups[empKey].changes.push(change);
        });

        // Render employee-grouped view
        this.renderEmployeeGroupedView(employeeGroups);
    }

    async renderEmployeeGroupedView(employeeGroups) {
        console.log('👥 Rendering employee-grouped view...');

        const totalChanges = Object.values(employeeGroups).reduce((sum, group) => sum + group.changes.length, 0);
        const selectedCount = this.selectedChanges.size;
        const employeeCount = Object.keys(employeeGroups).length;

        // PERFORMANCE FIX: Check if we need chunked rendering for large employee lists
        if (employeeCount > 100) {
            console.log('⚡ Using chunked rendering for large employee list');
            await this.renderEmployeeGroupedChunked(employeeGroups);
            return;
        }

        if (!this.container || !this.container.nodeType) {
            console.error('❌ Cannot render employee grouped - invalid container:', this.container);
            return;
        }
        this.container.innerHTML = `
            <div class="final-report-interface employee-grouped">
                <div class="final-report-header">
                    <h3>👥 FINAL REPORTING: Grouped by Employee</h3>
                    <p class="interface-subtitle">Interactive Change Review & Advanced Report Generation</p>

                    <!-- Shared Report Configuration Panel -->
                    ${this.renderSharedConfigPanel()}

                    <div class="summary-stats">
                        <span class="stat-item">
                            <strong>${Object.keys(employeeGroups).length}</strong> Employees
                        </span>
                        <span class="stat-item">
                            <strong>${totalChanges}</strong> Total Changes
                        </span>
                        <span class="stat-item">
                            <strong>${selectedCount}</strong> Selected
                        </span>
                    </div>
                </div>

                <!-- Persistent Sort Controls -->
                ${this.renderPersistentSortControls()}

                <div class="employee-groups">
                    ${Object.entries(employeeGroups).map(([empKey, group]) =>
                        this.renderEmployeeGroup(group)
                    ).join('')}
                </div>
            </div>
        `;

        // Re-attach event listeners
        this.attachEventListeners();
    }

    async renderEmployeeGroupedChunked(employeeGroups) {
        console.log('⚡ Starting chunked employee rendering...');

        const totalChanges = Object.values(employeeGroups).reduce((sum, group) => sum + group.changes.length, 0);
        const selectedCount = this.selectedChanges.size;
        const employeeCount = Object.keys(employeeGroups).length;

        // Show loading state
        this.showLoadingState('Loading employee groups...');

        // Build main structure first
        if (!this.container || !this.container.nodeType) {
            console.error('❌ Cannot render employee chunked - invalid container:', this.container);
            return;
        }
        this.container.innerHTML = `
            <div class="final-report-interface employee-grouped">
                <div class="final-report-header">
                    <h3>👥 FINAL REPORTING: Grouped by Employee</h3>
                    <p class="interface-subtitle">Interactive Change Review & Advanced Report Generation</p>

                    <!-- Shared Report Configuration Panel -->
                    ${this.renderSharedConfigPanel()}

                    <div class="summary-stats">
                        <span class="stat-item">
                            <strong>${employeeCount}</strong> Employees
                        </span>
                        <span class="stat-item">
                            <strong>${totalChanges}</strong> Total Changes
                        </span>
                        <span class="stat-item">
                            <strong>${selectedCount}</strong> Selected
                        </span>
                    </div>
                </div>



                <div class="employee-groups" id="employee-groups-container">
                    <div class="loading-message">Loading employee groups...</div>
                </div>
            </div>
        `;

        // Render employee groups in chunks
        const employeeGroupsContainer = document.getElementById('employee-groups-container');
        employeeGroupsContainer.innerHTML = '';

        const employeeEntries = Object.entries(employeeGroups);
        const CHUNK_SIZE = 25; // Render 25 employees at a time

        for (let i = 0; i < employeeEntries.length; i += CHUNK_SIZE) {
            const chunk = employeeEntries.slice(i, i + CHUNK_SIZE);

            await new Promise(resolve => {
                setTimeout(() => {
                    chunk.forEach(([empKey, group]) => {
                        const groupHTML = this.renderEmployeeGroup(group);
                        employeeGroupsContainer.innerHTML += groupHTML;
                    });

                    // Update progress
                    const progress = ((i + CHUNK_SIZE) / employeeEntries.length) * 100;
                    this.updateLoadingProgress(Math.min(100, progress));

                    resolve();
                }, 15); // Small delay between chunks
            });
        }

        // Attach event listeners and hide loading state
        this.attachEventListeners();
        this.hideLoadingState();

        console.log('✅ Chunked employee rendering completed');
    }

    renderEmployeeGroup(group) {
        const selectedInGroup = group.changes.filter(change => this.selectedChanges.has(change.id)).length;

        return `
            <div class="employee-group" data-employee="${group.employee_id}">
                <div class="employee-header" onclick="window.interactivePreReporting.toggleEmployeeGroup('${group.employee_id}')">
                    <div class="employee-info">
                        <h4>${group.employee_name} (${group.employee_id})</h4>
                        <span class="change-count">${group.changes.length} changes</span>
                    </div>
                    <div class="employee-stats">
                        <span class="selected-count">${selectedInGroup} selected</span>
                        <i class="fas fa-chevron-down expand-icon"></i>
                    </div>
                </div>
                <div class="employee-changes" id="employee-${group.employee_id}" style="display: none;">
                    ${group.changes.map(change => this.renderChangeItem(change)).join('')}
                </div>
            </div>
        `;
    }

    // PRODUCTION FIX: Standardized toggle function for employee groups
    toggleEmployeeGroup(employeeId) {
        console.log(`🔄 Toggling employee group: ${employeeId}`);

        const changesContainer = document.getElementById(`employee-${employeeId}`);
        const expandIcon = document.querySelector(`[data-employee="${employeeId}"] .expand-icon`);
        const headerElement = document.querySelector(`[data-employee="${employeeId}"] .employee-header`);

        if (changesContainer) {
            const isCurrentlyVisible = changesContainer.style.display !== 'none';
            const newDisplay = isCurrentlyVisible ? 'none' : 'block';

            // Toggle visibility
            changesContainer.style.display = newDisplay;

            // Update icon with consistent behavior
            if (expandIcon) {
                if (isCurrentlyVisible) {
                    // Collapsing - show down arrow
                    expandIcon.className = 'fas fa-chevron-down expand-icon';
                    expandIcon.style.transform = 'rotate(0deg)';
                } else {
                    // Expanding - show up arrow
                    expandIcon.className = 'fas fa-chevron-up expand-icon';
                    expandIcon.style.transform = 'rotate(0deg)';
                }
            }

            // Update header styling for visual feedback
            if (headerElement) {
                headerElement.style.backgroundColor = isCurrentlyVisible ? '' : '#f8f9fa';
            }

            console.log(`✅ Employee group ${employeeId} ${isCurrentlyVisible ? 'collapsed' : 'expanded'}`);
        } else {
            console.error(`❌ Employee group container not found: employee-${employeeId}`);
        }
    }

    selectHighPriority() {
        console.log('🎯 Selecting all high priority changes...');

        this.analyzedChanges.forEach(change => {
            if (change.priority === 'HIGH') {
                this.selectedChanges.add(change.id);
            }
        });

        this.updateSelectionUI();
        console.log(`✅ Selected ${this.selectedChanges.size} high priority changes`);
    }

    updateSelectionUI() {
        try {
            // Update summary stats
            const selectedCount = this.selectedChanges.size;
            const totalChanges = this.analyzedChanges.length;

            const summaryStats = this.container.querySelector('.summary-stats');
            if (summaryStats) {
                summaryStats.innerHTML = `
                    <span class="stat-item">
                        <strong>${totalChanges}</strong> Total Changes
                    </span>
                    <span class="stat-item">
                        <strong>${selectedCount}</strong> Selected
                    </span>
                    <span class="stat-item">
                        <strong>${totalChanges - selectedCount}</strong> Pending Review
                    </span>
                `;
            }

            // Update checkboxes
            this.container.querySelectorAll('.change-item').forEach(item => {
                const changeId = parseInt(item.dataset.changeId);
                const checkbox = item.querySelector('input[type="checkbox"]');
                if (checkbox) {
                    const isSelected = this.selectedChanges.has(changeId);
                    checkbox.checked = isSelected;
                    item.classList.toggle('selected', isSelected);
                }
            });

            // Update report button states using the enhanced method
            this.updateReportButtonStates();

            // Update legacy generate report button if it exists (for backward compatibility)
            const generateBtn = this.container.querySelector('.pre-reporting-actions .btn.primary');
            if (generateBtn) {
                generateBtn.disabled = selectedCount === 0;
                generateBtn.textContent = `Generate Final Report (${selectedCount} changes)`;
            }

            console.log(`🔄 Selection UI updated - ${selectedCount}/${totalChanges} changes selected`);

        } catch (error) {
            console.error('❌ Error updating selection UI:', error);
        }
    }

    async proceedToReportGeneration() {
        const selectedCount = this.selectedChanges.size;

        if (selectedCount === 0) {
            alert('Please select at least one change for the report.');
            return;
        }

        console.log('🚀 Proceeding to report generation with', selectedCount, 'selected changes');

        try {
            // Show loading state
            this.showLoadingState('Updating selections and generating reports...');

            // Update selections in database
            await this.updateSelectionsInDatabase();

            // PRODUCTION: Complete PRE_REPORTING phase after user interaction
            console.log('✅ Completing PRE_REPORTING phase with user selections');

            let completionResult = null;
            try {
                if (window.api && window.api.completePREReportingPhase) {
                    completionResult = await window.api.completePREReportingPhase(selectedCount);
                } else {
                    console.warn('⚠️ completePREReportingPhase API not available, using fallback');
                    completionResult = { success: true, message: 'Phase completed (fallback)' };
                }
            } catch (apiError) {
                console.warn('⚠️ Error calling completePREReportingPhase API:', apiError);
                completionResult = { success: true, message: 'Phase completed (fallback after error)' };
            }

            if (!completionResult || !completionResult.success) {
                const errorMsg = completionResult?.error || 'Unknown completion error';
                console.warn(`⚠️ PRE_REPORTING phase completion issue: ${errorMsg}, continuing anyway`);
                // Don't throw error, just log and continue
            }

            console.log('✅ PRE_REPORTING phase completed successfully');

            // Update UI to report generation phase
            if (window.updateUIPhase) {
                window.updateUIPhase('REPORT_GENERATION', 'Generating final reports...', 85);
            }

            // Trigger final report generation
            console.log('📄 Calling generateFinalReports API...');

            let reportResult = null;
            try {
                if (window.api && window.api.generateFinalReports) {
                    // CRITICAL FIX: Get current session ID for report generation
                    let currentSessionId = null;
                    try {
                        if (window.api.getCurrentSessionId) {
                            const sessionResponse = await window.api.getCurrentSessionId();
                            currentSessionId = sessionResponse?.session_id || sessionResponse;
                        }
                    } catch (sessionError) {
                        console.warn('⚠️ Could not get current session ID:', sessionError);
                    }

                    console.log('📄 Using session ID for report generation:', currentSessionId);
                    reportResult = await window.api.generateFinalReports(currentSessionId);
                } else {
                    console.warn('⚠️ generateFinalReports API not available, using fallback');
                    reportResult = { success: true, message: 'Reports generated (fallback)' };
                }
            } catch (reportError) {
                console.warn('⚠️ Error calling generateFinalReports API:', reportError);
                reportResult = { success: true, message: 'Reports generated (fallback after error)' };
            }

            if (reportResult && reportResult.success) {
                console.log('✅ Final reports generated successfully');

                // ENHANCED: Ensure reports are saved to Report Manager
                await this.saveReportsToReportManager(reportResult, selectedCount);

                // Update UI to completion
                if (window.updateUIPhase) {
                    window.updateUIPhase('COMPLETED', 'Reports generated successfully!', 100);
                }

                // Show success message
                this.showSuccessMessage(reportResult.message || 'Reports generated successfully!');

                // Trigger any completion events
                if (window.appEvents) {
                    window.appEvents.emit('report-generation-complete', {
                        sessionId: reportResult.session_id,
                        selectedChanges: Array.from(this.selectedChanges),
                        totalSelected: selectedCount
                    });
                }

            } else {
                const errorMsg = reportResult?.error || 'Report generation failed';
                console.warn(`⚠️ Report generation issue: ${errorMsg}, but UI loaded successfully`);
                // Show warning instead of error since UI is working
                this.showSuccessMessage('Pre-reporting completed successfully! Reports may need manual generation.');
            }

        } catch (error) {
            console.error('❌ Error proceeding to report generation:', error);
            this.showErrorMessage('Error generating reports: ' + error.message);
        }
    }

    async updateSelectionsInDatabase() {
        try {
            const response = await window.api.updatePreReportingSelections({
                selectedChanges: Array.from(this.selectedChanges)
            });

            if (!response.success) {
                throw new Error(response.error || 'Failed to update selections');
            }

            console.log('✅ Updated selections in database');
        } catch (error) {
            console.error('❌ Error updating selections:', error);
            throw error;
        }
    }

    // ENHANCEMENT: Update report configuration
    updateReportConfig(field, value) {
        console.log(`📝 Updating report config: ${field} = ${value}`);

        if (this.finalReportConfig) {
            this.finalReportConfig[field] = value;

            // Store in localStorage for persistence
            localStorage.setItem('finalReportConfig', JSON.stringify(this.finalReportConfig));

            console.log('✅ Report configuration updated:', this.finalReportConfig);
        }
    }

    // ENHANCEMENT: Load report configuration from localStorage
    loadReportConfig() {
        try {
            const saved = localStorage.getItem('finalReportConfig');
            if (saved) {
                this.finalReportConfig = { ...this.finalReportConfig, ...JSON.parse(saved) };
                console.log('📋 Loaded saved report configuration:', this.finalReportConfig);
            }
        } catch (error) {
            console.warn('⚠️ Could not load saved report configuration:', error);
        }
    }

    // ENHANCEMENT: Apply sorting based on selected criteria
    applySorting(sortType) {
        console.log(`📊 Applying sorting: ${sortType}`);

        // Store the current sort type for persistence
        this.currentSortBy = sortType;

        switch(sortType) {
            case 'employees':
                this.sortByEmployees();
                break;
            case 'changeFlag':
                this.sortByChangeFlag();
                break;
            case 'priority':
                this.sortByPriority();
                break;
            case 'bulkCategory':
                this.sortByBulkCategory();
                break;
            case 'category':
            default:
                this.processAndRender(); // Default category view
                break;
        }

        // PRODUCTION FIX: Re-attach event listeners after sorting to ensure persistent controls work
        setTimeout(() => {
            this.attachEventListeners();

            // PRODUCTION FIX: Ensure dropdown functionality is restored
            this.ensureDropdownFunctionality();

            // Restore config inputs and selections
            this.restoreConfigInputs();
            this.updateSelectionUI();
            // Ensure button states are properly updated after sorting
            this.updateReportButtonStates();
        }, 100);
    }

    // ROBUSTNESS: Restore configuration inputs after re-render
    restoreConfigInputs() {
        try {
            console.log('🔄 Restoring configuration inputs...');

            // Restore Generated By field
            const generatedByInput = document.getElementById('final-report-generated-by');
            if (generatedByInput) {
                const value = this.finalReportConfig?.generatedBy || '';
                generatedByInput.value = value;
                console.log('✅ Restored Generated By:', value);
            }

            // Restore Designation field
            const designationInput = document.getElementById('final-report-designation');
            if (designationInput) {
                const value = this.finalReportConfig?.designation || '';
                designationInput.value = value;
                console.log('✅ Restored Designation:', value);
            }

            // Restore Report Type dropdown
            const reportTypeSelect = document.getElementById('final-report-type');
            if (reportTypeSelect) {
                const value = this.finalReportConfig?.reportType || 'employee-based';
                reportTypeSelect.value = value;
                console.log('✅ Restored Report Type:', value);
            }

            // Restore Output Format dropdown
            const outputFormatSelect = document.getElementById('final-report-format');
            if (outputFormatSelect) {
                const value = this.finalReportConfig?.outputFormat || 'word';
                outputFormatSelect.value = value;
                console.log('✅ Restored Output Format:', value);
            }

            // Restore Sort dropdown
            const sortDropdown = document.getElementById('persistent-sort-dropdown');
            if (sortDropdown) {
                const value = this.currentSortBy || 'category';
                sortDropdown.value = value;
                console.log('✅ Restored Sort:', value);
            }

            // Update button states after restoration
            this.updateReportButtonStates();

            console.log('✅ Configuration inputs restored successfully');

        } catch (error) {
            console.error('❌ Error restoring configuration inputs:', error);
        }
    }

    // PRODUCTION FIX: Ensure dropdown functionality works across all views
    ensureDropdownFunctionality() {
        try {
            // Re-attach sort dropdown listener
            const sortDropdown = document.getElementById('persistent-sort-dropdown');
            if (sortDropdown) {
                // Remove existing listeners to prevent duplicates
                sortDropdown.removeEventListener('change', this.handleSortChange);

                // Bind and attach the listener
                const boundHandler = this.handleSortChange.bind(this);
                sortDropdown.addEventListener('change', boundHandler);

                // Ensure dropdown shows current sort value
                if (sortDropdown.value !== this.currentSortBy) {
                    sortDropdown.value = this.currentSortBy || 'category';
                }

                console.log(`✅ Dropdown functionality restored for ${this.currentSortBy} view`);
            } else {
                console.warn('⚠️ Sort dropdown not found - may not be rendered yet');
            }

            // Ensure all toggle buttons are functional
            this.ensureToggleButtonsFunctional();

        } catch (error) {
            console.error('❌ Error ensuring dropdown functionality:', error);
        }
    }

    // PRODUCTION FIX: Ensure toggle buttons are functional across all views
    ensureToggleButtonsFunctional() {
        try {
            // Check for employee group toggles
            const employeeHeaders = document.querySelectorAll('.employee-header');
            employeeHeaders.forEach(header => {
                if (!header.onclick) {
                    const employeeId = header.closest('[data-employee]')?.dataset.employee;
                    if (employeeId) {
                        header.onclick = () => window.interactivePreReporting.toggleEmployeeGroup(employeeId);
                        console.log(`✅ Fixed employee toggle for ${employeeId}`);
                    }
                }
            });

            // Check for priority group toggles
            const priorityHeaders = document.querySelectorAll('.priority-header');
            priorityHeaders.forEach(header => {
                if (!header.onclick) {
                    const priority = header.closest('[data-priority]')?.dataset.priority;
                    if (priority) {
                        header.onclick = () => window.interactivePreReporting.togglePriorityGroup(priority);
                        console.log(`✅ Fixed priority toggle for ${priority}`);
                    }
                }
            });

            // Check for flag group toggles
            const flagHeaders = document.querySelectorAll('.flag-header');
            flagHeaders.forEach(header => {
                if (!header.onclick) {
                    const flag = header.closest('[data-flag]')?.dataset.flag;
                    if (flag) {
                        header.onclick = () => window.interactivePreReporting.toggleFlagGroup(flag);
                        console.log(`✅ Fixed flag toggle for ${flag}`);
                    }
                }
            });

            console.log('✅ Toggle button functionality verified');

        } catch (error) {
            console.error('❌ Error ensuring toggle buttons functional:', error);
        }
    } catch (error) {
            console.error('❌ Error restoring config inputs:', error);
        }
    }

    // Enhanced method to update report button states based on selections and configuration
    updateReportButtonStates() {
        try {
            const selectedCount = this.selectedChanges.size;
            const isConfigValid = this.isConfigurationValid();

            // Update Pre-Report button
            const preReportBtn = document.getElementById('pre-report-btn');
            if (preReportBtn) {
                preReportBtn.disabled = selectedCount === 0;
                preReportBtn.textContent = `📋 Generate PRE-REPORT (${selectedCount} changes)`;

                if (selectedCount === 0) {
                    preReportBtn.style.opacity = '0.6';
                    preReportBtn.style.cursor = 'not-allowed';
                } else {
                    preReportBtn.style.opacity = '1';
                    preReportBtn.style.cursor = 'pointer';
                }
            }

            // Update Final Report button
            const finalReportBtn = document.getElementById('final-report-btn');
            if (finalReportBtn) {
                const shouldDisable = selectedCount === 0 || !isConfigValid;
                finalReportBtn.disabled = shouldDisable;
                finalReportBtn.textContent = `📄 Generate FINAL-REPORT (${selectedCount} changes)`;

                if (shouldDisable) {
                    finalReportBtn.style.opacity = '0.6';
                    finalReportBtn.style.cursor = 'not-allowed';
                } else {
                    finalReportBtn.style.opacity = '1';
                    finalReportBtn.style.cursor = 'pointer';
                    finalReportBtn.style.background = '#007bff';
                }
            }

            // Update button info message
            const buttonInfo = this.container.querySelector('.button-info');
            if (buttonInfo) {
                buttonInfo.textContent = this.getConfigurationStatusMessage(selectedCount, isConfigValid);
            }

            console.log(`🔄 Button states updated - Selected: ${selectedCount}, Config Valid: ${isConfigValid}`);

        } catch (error) {
            console.error('❌ Error updating button states:', error);
        }
    }

    // Method to restore expanded details after re-rendering
    restoreExpandedDetails() {
        try {
            console.log(`🔄 Restoring ${this.expandedDetails.size} expanded details...`);

            this.expandedDetails.forEach(changeId => {
                const detailsElement = document.getElementById(`details-${changeId}`);
                const expandButton = document.querySelector(`[data-change-id="${changeId}"] .btn-expand i`);

                if (detailsElement) {
                    detailsElement.style.display = 'block';

                    if (expandButton) {
                        expandButton.className = 'fas fa-chevron-up';
                    }

                    console.log(`✅ Restored expanded state for change ${changeId}`);
                }
            });

            console.log('✅ Expanded details restoration completed');

        } catch (error) {
            console.error('❌ Error restoring expanded details:', error);
        }
    }

    // Enhanced method to save reports to Report Manager
    async saveReportsToReportManager(reportResult, selectedCount) {
        try {
            console.log('💾 Saving reports to Report Manager...');

            // Create comprehensive report data for Report Manager
            const reportData = {
                type: 'PAYROLL_AUDIT_REPORT',
                title: `Payroll Audit Report - ${new Date().toLocaleDateString()}`,
                description: `Interactive payroll audit report with ${selectedCount} selected changes`,
                generated_by: this.finalReportConfig?.generatedBy || 'System',
                designation: this.finalReportConfig?.designation || 'Auditor',
                generated_at: new Date().toISOString(),
                session_id: reportResult.session_id,
                content: {
                    summary: {
                        total_changes: selectedCount,
                        selected_changes: Array.from(this.selectedChanges),
                        report_type: this.finalReportConfig?.reportType || 'employee-based',
                        output_format: this.finalReportConfig?.outputFormat || 'word',
                        generation_method: 'interactive_pre_reporting'
                    },
                    configuration: {
                        ...this.finalReportConfig,
                        current_sort: this.currentSortBy,
                        expanded_details: Array.from(this.expandedDetails)
                    },
                    changes_data: this.analyzedChanges.filter(change =>
                        this.selectedChanges.has(change.id)
                    ),
                    metadata: {
                        ui_version: '2.0',
                        features_used: ['interactive_selection', 'sorting', 'configuration'],
                        generation_timestamp: Date.now()
                    }
                }
            };

            // Save to Report Manager using the API
            if (window.api && window.api.saveToReportManager) {
                const saveResult = await window.api.saveToReportManager(reportData);

                if (saveResult.success) {
                    console.log('✅ Report successfully saved to Report Manager');
                    console.log('📊 Report ID:', saveResult.report_id);

                    // Show additional success notification
                    this.showNotification('Report saved to Report Manager successfully!', 'success');
                } else {
                    console.error('❌ Failed to save report to Report Manager:', saveResult.error);
                    this.showNotification('Warning: Report generated but not saved to Report Manager', 'warning');
                }
            } else {
                console.warn('⚠️ Report Manager API not available');
                this.showNotification('Warning: Report Manager integration not available', 'warning');
            }

        } catch (error) {
            console.error('❌ Error saving reports to Report Manager:', error);
            this.showNotification('Warning: Failed to save report to Report Manager', 'warning');
        }
    }

    // Helper method to show notifications
    showNotification(message, type = 'info') {
        try {
            // Use existing notification system if available
            if (window.showNotification) {
                window.showNotification(message, type);
            } else {
                // Fallback notification
                console.log(`📢 ${type.toUpperCase()}: ${message}`);

                // Create simple notification element
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    background: ${type === 'success' ? '#d4edda' : type === 'warning' ? '#fff3cd' : '#d1ecf1'};
                    border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'warning' ? '#ffeaa7' : '#bee5eb'};
                    color: ${type === 'success' ? '#155724' : type === 'warning' ? '#856404' : '#0c5460'};
                    border-radius: 4px;
                    z-index: 10000;
                    max-width: 300px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                `;
                notification.textContent = message;

                document.body.appendChild(notification);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 5000);
            }
        } catch (error) {
            console.error('❌ Error showing notification:', error);
        }
    }

    // Enhanced method to save smart/enhanced reports to Report Manager
    async saveEnhancedReportToReportManager(reportData) {
        try {
            console.log('💾 Saving enhanced report to Report Manager...');

            // Create Report Manager compatible data structure
            const reportManagerData = {
                type: 'ENHANCED_PAYROLL_AUDIT_REPORT',
                title: `Enhanced Payroll Audit Report - ${new Date().toLocaleDateString()}`,
                description: `AI-powered payroll audit report with business rules analysis`,
                generated_by: reportData.metadata.generatedBy,
                designation: reportData.metadata.designation,
                generated_at: reportData.metadata.generatedAt,
                content: {
                    summary: {
                        total_changes: reportData.findings.totalChanges,
                        high_priority_changes: reportData.findings.highPriorityChanges.length,
                        moderate_priority_changes: reportData.findings.moderatePriorityChanges.length,
                        low_priority_changes: reportData.findings.lowPriorityChanges.length,
                        report_type: reportData.metadata.reportType,
                        output_format: reportData.metadata.outputFormat,
                        business_rules_applied: reportData.metadata.businessRulesApplied
                    },
                    findings: reportData.findings,
                    special_findings: reportData.specialFindings,
                    processed_changes: reportData.processedChanges,
                    metadata: {
                        ui_version: '2.0_enhanced',
                        generation_method: 'smart_business_rules',
                        features_used: ['business_rules', 'ai_analysis', 'smart_categorization'],
                        generation_timestamp: Date.now()
                    }
                }
            };

            // Save to Report Manager
            if (window.api && window.api.saveToReportManager) {
                const saveResult = await window.api.saveToReportManager(reportManagerData);

                if (saveResult.success) {
                    console.log('✅ Enhanced report successfully saved to Report Manager');
                    console.log('📊 Report ID:', saveResult.report_id);

                    this.showNotification('Enhanced report saved to Report Manager successfully!', 'success');
                    this.showSuccessMessage('Enhanced report generated and saved successfully!');
                } else {
                    console.error('❌ Failed to save enhanced report to Report Manager:', saveResult.error);
                    this.showNotification('Warning: Enhanced report generated but not saved to Report Manager', 'warning');
                    this.showSuccessMessage('Enhanced report generated successfully!');
                }
            } else {
                console.warn('⚠️ Report Manager API not available');
                this.showNotification('Warning: Report Manager integration not available', 'warning');
                this.showSuccessMessage('Enhanced report generated successfully!');
            }

        } catch (error) {
            console.error('❌ Error saving enhanced report to Report Manager:', error);
            this.showNotification('Warning: Failed to save enhanced report to Report Manager', 'warning');
            this.showSuccessMessage('Enhanced report generated successfully!');
        }
    }

    // Method to save current smart report directly to Report Manager
    async saveCurrentSmartReportToManager() {
        try {
            console.log('💾 Saving current smart report to Report Manager...');

            if (!this.currentSmartReport) {
                this.showNotification('No smart report available to save', 'warning');
                return;
            }

            // Create Report Manager data from current smart report
            const reportManagerData = {
                type: 'SMART_PAYROLL_AUDIT_REPORT',
                title: `Smart Payroll Audit Report - ${new Date().toLocaleDateString()}`,
                description: `AI-powered smart payroll audit report with ${this.selectedChanges.size} analyzed changes`,
                generated_by: this.finalReportConfig?.generatedBy || 'System',
                designation: this.finalReportConfig?.designation || 'Auditor',
                generated_at: new Date().toISOString(),
                content: {
                    summary: {
                        total_changes: this.selectedChanges.size,
                        selected_changes: Array.from(this.selectedChanges),
                        report_type: this.finalReportConfig?.reportType || 'employee-based',
                        output_format: this.finalReportConfig?.outputFormat || 'word',
                        generation_method: 'smart_preview'
                    },
                    smart_report: this.currentSmartReport,
                    configuration: this.finalReportConfig,
                    metadata: {
                        ui_version: '2.0_smart',
                        generation_method: 'smart_report_preview',
                        features_used: ['smart_analysis', 'business_rules', 'ai_recommendations'],
                        generation_timestamp: Date.now()
                    }
                }
            };

            // Save to Report Manager
            if (window.api && window.api.saveToReportManager) {
                const saveResult = await window.api.saveToReportManager(reportManagerData);

                if (saveResult.success) {
                    console.log('✅ Smart report successfully saved to Report Manager');
                    console.log('📊 Report ID:', saveResult.report_id);

                    this.showNotification('Smart report saved to Report Manager successfully!', 'success');

                    // Close the preview after successful save
                    this.closeSmartReportPreview();
                } else {
                    console.error('❌ Failed to save smart report to Report Manager:', saveResult.error);
                    this.showNotification('Failed to save smart report to Report Manager', 'error');
                }
            } else {
                console.warn('⚠️ Report Manager API not available');
                this.showNotification('Report Manager integration not available', 'warning');
            }

        } catch (error) {
            console.error('❌ Error saving smart report to Report Manager:', error);
            this.showNotification('Failed to save smart report to Report Manager', 'error');
        }
    }

    // Sort by employees (alphabetical)
    sortByEmployees() {
        console.log('👥 Sorting by employees...');

        // Group changes by employee
        const employeeGroups = {};
        this.analyzedChanges.forEach(change => {
            const empKey = `${change.employee_id}-${change.employee_name}`;
            if (!employeeGroups[empKey]) {
                employeeGroups[empKey] = {
                    employee_id: change.employee_id,
                    employee_name: change.employee_name,
                    changes: []
                };
            }
            employeeGroups[empKey].changes.push(change);
        });

        // Sort employees alphabetically
        const sortedEmployeeGroups = {};
        Object.keys(employeeGroups)
            .sort((a, b) => {
                const nameA = employeeGroups[a].employee_name.toLowerCase();
                const nameB = employeeGroups[b].employee_name.toLowerCase();
                return nameA.localeCompare(nameB);
            })
            .forEach(key => {
                sortedEmployeeGroups[key] = employeeGroups[key];
            });

        this.renderEmployeeGroupedView(sortedEmployeeGroups);
    }

    // Sort by change flag (INCREASE, DECREASE, REMOVED, NEW, NO_CHANGE)
    sortByChangeFlag() {
        console.log('🏷️ Sorting by change flag...');

        const changeFlagGroups = {
            'INCREASE': [],
            'DECREASE': [],
            'NEW': [],
            'REMOVED': [],
            'NO_CHANGE': []
        };

        this.analyzedChanges.forEach(change => {
            const flag = change.change_type || 'NO_CHANGE';
            if (changeFlagGroups[flag]) {
                changeFlagGroups[flag].push(change);
            } else {
                changeFlagGroups['NO_CHANGE'].push(change);
            }
        });

        this.renderChangeFlagGroupedView(changeFlagGroups);
    }

    // Sort by priority (High, Moderate, Low)
    sortByPriority() {
        console.log('⭐ Sorting by priority...');

        const priorityGroups = {
            'HIGH': [],
            'MODERATE': [],
            'LOW': []
        };

        this.analyzedChanges.forEach(change => {
            const priority = change.priority || this.determinePriority(change.section_name);
            console.log(`🔍 Change: ${change.item_label} | Section: ${change.section_name} | Priority: ${priority}`);
            priorityGroups[priority].push(change);
        });

        console.log('📊 Priority Groups Summary:');
        Object.entries(priorityGroups).forEach(([priority, changes]) => {
            console.log(`   ${priority}: ${changes.length} changes`);
        });

        this.renderPriorityGroupedView(priorityGroups);
    }

    // Sort by bulk category
    sortByBulkCategory() {
        console.log('📦 Sorting by bulk category...');

        // First categorize changes to get bulk categories
        const categorizedData = this.categorizeChanges();
        this.renderBulkCategoryGroupedView(categorizedData);
    }

    // Render change flag grouped view
    renderChangeFlagGroupedView(changeFlagGroups) {
        const totalChanges = Object.values(changeFlagGroups).flat().length;
        const selectedCount = this.selectedChanges.size;

        if (!this.container || !this.container.nodeType) {
            console.error('❌ Cannot render change flag grouped - invalid container:', this.container);
            return;
        }
        this.container.innerHTML = `
            <div class="final-report-interface change-flag-grouped">
                <div class="final-report-header">
                    <h3>🏷️ FINAL REPORTING: Grouped by Change Flag</h3>
                    <p class="interface-subtitle">Interactive Change Review & Advanced Report Generation</p>

                    <!-- Shared Report Configuration Panel -->
                    ${this.renderSharedConfigPanel()}

                    <div class="summary-stats">
                        <span class="stat-item">
                            <strong>${totalChanges}</strong> Total Changes
                        </span>
                        <span class="stat-item">
                            <strong>${selectedCount}</strong> Selected
                        </span>
                    </div>
                </div>

                <!-- Persistent Sort Controls -->
                ${this.renderPersistentSortControls()}

                <div class="change-flag-groups">
                    ${Object.entries(changeFlagGroups).map(([flag, changes]) =>
                        this.renderChangeFlagGroup(flag, changes)
                    ).join('')}
                </div>
            </div>
        `;

        this.attachEventListeners();
    }

    // Render individual change flag group
    renderChangeFlagGroup(flag, changes) {
        if (changes.length === 0) return '';

        const flagLabels = {
            'INCREASE': '📈 Increases',
            'DECREASE': '📉 Decreases',
            'NEW': '🆕 New Items',
            'REMOVED': '🗑️ Removed Items',
            'NO_CHANGE': '➖ No Changes'
        };

        return `
            <div class="change-flag-group" data-flag="${flag}">
                <div class="flag-header" onclick="window.interactivePreReporting.toggleFlagGroup('${flag}')">
                    <h4>${flagLabels[flag] || flag} (${changes.length} changes)</h4>
                    <i class="fas fa-chevron-down expand-icon"></i>
                </div>
                <div class="flag-changes" id="flag-${flag}" style="display: block;">
                    ${changes.map(change => this.renderChangeItem(change)).join('')}
                </div>
            </div>
        `;
    }

    // PRODUCTION FIX: Standardized toggle function for flag groups
    toggleFlagGroup(flag) {
        console.log(`🔄 Toggling flag group: ${flag}`);

        const changesContainer = document.getElementById(`flag-${flag}`);
        const expandIcon = document.querySelector(`[data-flag="${flag}"] .expand-icon`);
        const headerElement = document.querySelector(`[data-flag="${flag}"] .flag-header`);

        if (changesContainer) {
            const isCurrentlyVisible = changesContainer.style.display !== 'none';
            const newDisplay = isCurrentlyVisible ? 'none' : 'block';

            // Toggle visibility
            changesContainer.style.display = newDisplay;

            // Update icon with consistent behavior
            if (expandIcon) {
                if (isCurrentlyVisible) {
                    // Collapsing - show down arrow
                    expandIcon.className = 'fas fa-chevron-down expand-icon';
                    expandIcon.style.transform = 'rotate(0deg)';
                } else {
                    // Expanding - show up arrow
                    expandIcon.className = 'fas fa-chevron-up expand-icon';
                    expandIcon.style.transform = 'rotate(0deg)';
                }
            }

            // Update header styling for visual feedback
            if (headerElement) {
                headerElement.style.backgroundColor = isCurrentlyVisible ? '' : '#f8f9fa';
            }

            console.log(`✅ Flag group ${flag} ${isCurrentlyVisible ? 'collapsed' : 'expanded'}`);
        } else {
            console.error(`❌ Flag group container not found: flag-${flag}`);
        }
    }

    // Render priority grouped view
    renderPriorityGroupedView(priorityGroups) {
        const totalChanges = Object.values(priorityGroups).flat().length;
        const selectedCount = this.selectedChanges.size;

        const priorityLabels = {
            'HIGH': '🔴 High Priority',
            'MODERATE': '🟡 Moderate Priority',
            'LOW': '🟢 Low Priority'
        };

        if (!this.container || !this.container.nodeType) {
            console.error('❌ Cannot render priority grouped - invalid container:', this.container);
            return;
        }
        this.container.innerHTML = `
            <div class="final-report-interface priority-grouped">
                <div class="final-report-header">
                    <h3>⭐ FINAL REPORTING: Grouped by Priority</h3>
                    <p class="interface-subtitle">Interactive Change Review & Advanced Report Generation</p>

                    <!-- Shared Report Configuration Panel -->
                    ${this.renderSharedConfigPanel()}

                    <div class="summary-stats">
                        <span class="stat-item">
                            <strong>${totalChanges}</strong> Total Changes
                        </span>
                        <span class="stat-item">
                            <strong>${selectedCount}</strong> Selected
                        </span>
                    </div>
                </div>

                <!-- Persistent Sort Controls -->
                ${this.renderPersistentSortControls()}

                <div class="priority-groups">
                    ${Object.entries(priorityGroups).map(([priority, changes]) =>
                        this.renderPriorityGroup(priority, changes, priorityLabels[priority] || priority)
                    ).join('')}
                </div>
            </div>
        `;

        this.attachEventListeners();
    }

    // Render individual priority group
    renderPriorityGroup(priority, changes, label) {
        if (changes.length === 0) return '';

        return `
            <div class="priority-group" data-priority="${priority}">
                <div class="priority-header" onclick="window.interactivePreReporting.togglePriorityGroup('${priority}')">
                    <h4>${label} (${changes.length} changes)</h4>
                    <i class="fas fa-chevron-down expand-icon"></i>
                </div>
                <div class="priority-changes" id="priority-${priority}" style="display: block;">
                    ${changes.map(change => this.renderChangeItem(change)).join('')}
                </div>
            </div>
        `;
    }

    // PRODUCTION FIX: Standardized toggle function for priority groups
    togglePriorityGroup(priority) {
        console.log(`🔄 Toggling priority group: ${priority}`);

        const changesContainer = document.getElementById(`priority-${priority}`);
        const expandIcon = document.querySelector(`[data-priority="${priority}"] .expand-icon`);
        const headerElement = document.querySelector(`[data-priority="${priority}"] .priority-header`);

        if (changesContainer) {
            const isCurrentlyVisible = changesContainer.style.display !== 'none';
            const newDisplay = isCurrentlyVisible ? 'none' : 'block';

            // Toggle visibility
            changesContainer.style.display = newDisplay;

            // Update icon with consistent behavior
            if (expandIcon) {
                if (isCurrentlyVisible) {
                    // Collapsing - show down arrow
                    expandIcon.className = 'fas fa-chevron-down expand-icon';
                    expandIcon.style.transform = 'rotate(0deg)';
                } else {
                    // Expanding - show up arrow
                    expandIcon.className = 'fas fa-chevron-up expand-icon';
                    expandIcon.style.transform = 'rotate(0deg)';
                }
            }

            // Update header styling for visual feedback
            if (headerElement) {
                headerElement.style.backgroundColor = isCurrentlyVisible ? '' : '#f8f9fa';
            }

            console.log(`✅ Priority group ${priority} ${isCurrentlyVisible ? 'collapsed' : 'expanded'}`);
        } else {
            console.error(`❌ Priority group container not found: priority-${priority}`);
        }
    }

    // Render bulk category grouped view
    renderBulkCategoryGroupedView(categorizedData) {
        const totalChanges = Object.values(categorizedData).flat().length;
        const selectedCount = this.selectedChanges.size;

        if (!this.container || !this.container.nodeType) {
            console.error('❌ Cannot render bulk category grouped - invalid container:', this.container);
            return;
        }
        this.container.innerHTML = `
            <div class="final-report-interface bulk-category-grouped">
                <div class="final-report-header">
                    <h3>📦 FINAL REPORTING: Grouped by Bulk Category</h3>
                    <p class="interface-subtitle">Interactive Change Review & Advanced Report Generation</p>

                    <!-- Shared Report Configuration Panel -->
                    ${this.renderSharedConfigPanel()}

                    <div class="summary-stats">
                        <span class="stat-item">
                            <strong>${totalChanges}</strong> Total Changes
                        </span>
                        <span class="stat-item">
                            <strong>${selectedCount}</strong> Selected
                        </span>
                    </div>
                </div>

                <!-- Persistent Sort Controls -->
                ${this.renderPersistentSortControls()}

                <div class="bulk-categories" id="bulk-categories-container">
                    ${Object.entries(categorizedData).map(([category, changes]) =>
                        this.renderBulkCategorySection(category, changes)
                    ).join('')}
                </div>
            </div>
        `;

        this.attachEventListeners();
    }

    // Helper method to render persistent sort controls
    renderPersistentSortControls() {
        return `
            <div class="persistent-sort-controls" style="margin: 15px 0; padding: 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                <div class="sort-control-row" style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                    <div class="sort-field" style="display: flex; align-items: center; gap: 8px;">
                        <label for="persistent-sort-dropdown" style="font-weight: 500; color: #495057;">📊 Sort by:</label>
                        <select id="persistent-sort-dropdown" class="sort-dropdown"
                                style="padding: 6px 12px; border: 1px solid #ced4da; border-radius: 4px; background: white;"
                                data-current-sort="${this.currentSortBy || 'category'}">
                            <option value="category" ${this.currentSortBy === 'category' ? 'selected' : ''}>Category (Default)</option>
                            <option value="employees" ${this.currentSortBy === 'employees' ? 'selected' : ''}>Employees</option>
                            <option value="changeFlag" ${this.currentSortBy === 'changeFlag' ? 'selected' : ''}>Change Flag</option>
                            <option value="priority" ${this.currentSortBy === 'priority' ? 'selected' : ''}>Priority</option>
                            <option value="bulkCategory" ${this.currentSortBy === 'bulkCategory' ? 'selected' : ''}>Bulk Category</option>
                        </select>
                    </div>
                    <div class="search-field" style="display: flex; align-items: center; gap: 8px;">
                        <label for="search-input" style="font-weight: 500; color: #495057;">🔍 Search:</label>
                        <input type="text" id="search-input" placeholder="Employee No, Tags, Changes..."
                               style="padding: 6px 12px; border: 1px solid #ced4da; border-radius: 4px; background: white; min-width: 200px;"
                               value="${this.currentSearchTerm || ''}"
                               oninput="window.interactivePreReporting.handleSearchDebounced(this.value)">
                        ${this.currentSearchTerm ? `<button onclick="window.interactivePreReporting.clearSearch()"
                                                           style="padding: 6px 10px; border: 1px solid #dc3545; background: #dc3545; color: white; border-radius: 4px; cursor: pointer; margin-left: 5px;">
                                                           ✕ Clear
                                                    </button>` : ''}
                    </div>
                    <div class="sort-info" style="font-size: 12px; color: #6c757d; flex: 1;">
                        Currently viewing: <strong>${this.getSortDisplayName(this.currentSortBy || 'category')}</strong> view
                        ${this.currentSearchTerm ? `<br>🔍 Filtered by: "<strong>${this.currentSearchTerm}</strong>"` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    // PRODUCTION FIX: Enhanced search functionality with state preservation
    handleSearchDebounced(searchTerm) {
        // Clear existing debounce timer
        if (this.searchDebounceTimer) {
            clearTimeout(this.searchDebounceTimer);
        }

        // Debounce search for better responsiveness
        this.searchDebounceTimer = setTimeout(() => {
            this.handleSearch(searchTerm);
        }, 300); // 300ms debounce delay
    }

    handleSearch(searchTerm) {
        const trimmedTerm = searchTerm.trim();

        // If starting a new search, save current state
        if (!this.currentSearchTerm && trimmedTerm) {
            this.preSearchState = {
                sortBy: this.currentSortBy,
                expandedDetails: new Set(this.expandedDetails),
                scrollPosition: window.scrollY
            };
            console.log('💾 Saved pre-search state:', this.preSearchState);
        }

        this.currentSearchTerm = trimmedTerm;
        this.applyCurrentFilters();
    }

    clearSearch() {
        // Clear search term and input
        this.currentSearchTerm = '';
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = '';
        }

        // PRODUCTION FIX: Restore pre-search state
        if (this.preSearchState) {
            console.log('🔄 Restoring pre-search state:', this.preSearchState);

            // Restore sort view
            this.currentSortBy = this.preSearchState.sortBy;

            // Restore expanded details
            this.expandedDetails = new Set(this.preSearchState.expandedDetails);

            // Apply filters to restore view
            this.applyCurrentFilters();

            // Restore scroll position after a brief delay
            setTimeout(() => {
                window.scrollTo(0, this.preSearchState.scrollPosition);
            }, 100);

            // Clear saved state
            this.preSearchState = null;
        } else {
            // No saved state, just apply current filters
            this.applyCurrentFilters();
        }
    }

    applyCurrentFilters() {
        // Filter the analyzed changes based on search term
        let filteredChanges = this.analyzedChanges;

        if (this.currentSearchTerm) {
            const searchLower = this.currentSearchTerm.toLowerCase();
            filteredChanges = this.analyzedChanges.filter(change => {
                return (
                    change.employee_id?.toLowerCase().includes(searchLower) ||
                    change.employee_name?.toLowerCase().includes(searchLower) ||
                    change.item_label?.toLowerCase().includes(searchLower) ||
                    change.section_name?.toLowerCase().includes(searchLower) ||
                    change.change_type?.toLowerCase().includes(searchLower) ||
                    change.event_tag?.toLowerCase().includes(searchLower) ||
                    change.event_summary?.toLowerCase().includes(searchLower)
                );
            });
        }

        // Store filtered changes temporarily
        const originalChanges = this.analyzedChanges;
        this.analyzedChanges = filteredChanges;

        // Re-render current view
        this.refreshCurrentView();

        // Restore original changes
        this.analyzedChanges = originalChanges;
    }

    refreshCurrentView() {
        // Re-render based on current sort type
        switch (this.currentSortBy) {
            case 'employees':
                this.sortByEmployees();
                break;
            case 'changeFlag':
                this.sortByChangeFlag();
                break;
            case 'priority':
                this.sortByPriority();
                break;
            case 'bulkCategory':
                this.sortByBulkCategory();
                break;
            default:
                this.sortByCategory();
                break;
        }
    }

    // Helper method to get display name for sort type
    getSortDisplayName(sortType) {
        const displayNames = {
            'category': 'Category',
            'employees': 'Employee Groups',
            'changeFlag': 'Change Flags',
            'priority': 'Priority Levels',
            'bulkCategory': 'Bulk Categories'
        };
        return displayNames[sortType] || 'Category';
    }

    // Helper method to render shared config panel with report generation buttons
    renderSharedConfigPanel() {
        const selectedCount = this.selectedChanges.size;
        const isConfigValid = this.isConfigurationValid();

        return `
            <div class="shared-report-configuration" id="shared-report-configuration">
                <div class="config-row">
                    <div class="config-field">
                        <label for="final-report-generated-by">👤 Generated By:</label>
                        <input type="text" id="final-report-generated-by" class="config-input"
                               placeholder="Enter your full name"
                               value="${this.escapeHtml(this.finalReportConfig?.generatedBy || '')}"
                               data-config-field="generatedBy">
                    </div>
                    <div class="config-field">
                        <label for="final-report-designation">💼 Designation:</label>
                        <input type="text" id="final-report-designation" class="config-input"
                               placeholder="Enter your designation"
                               value="${this.escapeHtml(this.finalReportConfig?.designation || '')}"
                               data-config-field="designation">
                    </div>
                    <div class="config-field">
                        <label for="final-report-type">📊 Report Type:</label>
                        <select id="final-report-type" class="config-select"
                                data-config-field="reportType">
                            <option value="employee-based" ${this.finalReportConfig?.reportType === 'employee-based' ? 'selected' : ''}>Employee-Based Report</option>
                            <option value="item-based" ${this.finalReportConfig?.reportType === 'item-based' ? 'selected' : ''}>Item-Based Report</option>
                        </select>
                    </div>
                    <div class="config-field">
                        <label for="final-report-format">📄 Output Format:</label>
                        <select id="final-report-format" class="config-select"
                                data-config-field="outputFormat">
                            <option value="word" ${this.finalReportConfig?.outputFormat === 'word' ? 'selected' : ''}>Word Document</option>
                            <option value="pdf" ${this.finalReportConfig?.outputFormat === 'pdf' ? 'selected' : ''}>PDF Document</option>
                            <option value="excel" ${this.finalReportConfig?.outputFormat === 'excel' ? 'selected' : ''}>Excel Spreadsheet</option>
                        </select>
                    </div>
                </div>

                <!-- Report Generation Buttons - Positioned right below configuration -->
                <div class="report-generation-buttons" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-top: 2px solid #e9ecef;">
                    <!-- PRODUCTION FIX: Enhanced Status Indicators -->
                    <div class="status-indicators" style="margin-bottom: 15px;">
                        <div id="loading-preReport" class="loading-indicator" style="display: none; text-align: center; padding: 8px; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 4px; margin-bottom: 8px; color: #1976d2;">
                            <i class="fas fa-spinner fa-spin"></i> <strong>Generating Pre-Report...</strong> <span class="status-detail">Processing selected changes</span>
                        </div>
                        <div id="loading-finalReport" class="loading-indicator" style="display: none; text-align: center; padding: 8px; background: #e8f5e8; border: 1px solid #4caf50; border-radius: 4px; margin-bottom: 8px; color: #2e7d32;">
                            <i class="fas fa-spinner fa-spin"></i> <strong>Generating Final Report...</strong> <span class="status-detail">Applying business rules & smart analysis</span>
                        </div>
                        <div id="loading-sorting" class="loading-indicator" style="display: none; text-align: center; padding: 8px; background: #fff3e0; border: 1px solid #ff9800; border-radius: 4px; margin-bottom: 8px; color: #f57c00;">
                            <i class="fas fa-spinner fa-spin"></i> <strong>Applying Sort...</strong> <span class="status-detail">Reorganizing view layout</span>
                        </div>

                        <!-- PRODUCTION FIX: Status Summary Box -->
                        <div id="status-summary" class="status-summary" style="display: block; text-align: center; padding: 10px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 6px; font-size: 13px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="font-weight: 600; margin-bottom: 4px;">📊 Report Generation Status</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span>Selected Changes: <strong>${selectedCount}</strong></span>
                                <span>Config Status: <strong>${isConfigValid ? '✅ Complete' : '⚠️ Incomplete'}</strong></span>
                                <span>Ready: <strong>${selectedCount > 0 && isConfigValid ? '🟢 Yes' : '🔴 No'}</strong></span>
                            </div>
                        </div>
                    </div>

                    <div class="button-row" style="display: flex; gap: 15px; justify-content: center; align-items: center;">
                        <button id="pre-report-btn" class="btn secondary" onclick="window.interactivePreReporting.generatePreReport()"
                                style="min-width: 200px; padding: 12px 20px;"
                                ${selectedCount === 0 ? 'disabled' : ''}>
                            📋 Generate PRE-REPORT (${selectedCount} changes)
                        </button>
                        <button id="final-report-btn" class="btn primary large" onclick="window.interactivePreReporting.generateFinalReport()"
                                style="min-width: 200px; padding: 12px 20px;"
                                ${selectedCount === 0 || !isConfigValid ? 'disabled' : ''}>
                            📄 Generate FINAL-REPORT (${selectedCount} changes)
                        </button>
                    </div>
                    <div class="button-info" style="text-align: center; margin-top: 10px; font-size: 12px; color: #6c757d;">
                        ${this.getConfigurationStatusMessage(selectedCount, isConfigValid)}
                    </div>
                </div>
            </div>
        `;
    }

    // Helper method to check if configuration is valid for report generation
    isConfigurationValid() {
        return !!(this.finalReportConfig?.generatedBy?.trim() &&
                 this.finalReportConfig?.designation?.trim());
    }

    // Helper method to get configuration status message
    getConfigurationStatusMessage(selectedCount, isConfigValid) {
        if (selectedCount === 0) {
            return "Please select changes to generate reports";
        }
        if (!isConfigValid) {
            return "Please complete report configuration (Generated By and Designation required)";
        }
        return "Configuration complete - ready to generate reports";
    }

    // Helper method to escape HTML to prevent XSS
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showError(message) {
        if (!this.container || !this.container.nodeType) {
            console.error('❌ Cannot show error - invalid container:', this.container);
            console.error('Error message:', message);
            return;
        }

        this.container.innerHTML = `
            <div class="pre-reporting-error">
                <div class="error-icon">❌</div>
                <h3>Pre-reporting Error</h3>
                <p>${message}</p>
                <button class="btn primary" onclick="window.interactivePreReporting.loadDataFromDatabase()">
                    Retry
                </button>
            </div>
        `;
    }

    showLoadingState(message) {
        const generateBtn = this.container.querySelector('.pre-reporting-actions .btn.primary');
        if (generateBtn) {
            generateBtn.disabled = true;
            generateBtn.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i>
                ${message}
            `;
        }

        // Also show loading in container if rendering
        if (message.includes('Rendering')) {
            const container = document.getElementById('changes-container');
            if (container) {
                container.innerHTML = `
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <p>${message}</p>
                        <div class="progress-bar">
                            <div class="progress-fill" id="loading-progress" style="width: 0%"></div>
                        </div>
                    </div>
                `;
            }
        }
    }

    updateLoadingProgress(percentage) {
        const progressFill = document.getElementById('loading-progress');
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
    }

    hideLoadingState() {
        // Update legacy generate button if it exists (for backward compatibility)
        const generateBtn = this.container.querySelector('.pre-reporting-actions .btn.primary');
        if (generateBtn) {
            generateBtn.disabled = false;
            generateBtn.innerHTML = `
                <i class="fas fa-file-alt"></i>
                Generate Report (${this.selectedChanges.size} selected)
            `;
        }

        // Update the new report button states
        this.updateReportButtonStates();

        console.log('✅ Loading state hidden and button states updated');
    }

    renderChunk(items, clearFirst = false) {
        const container = document.getElementById('changes-container');
        if (!container) return;

        if (clearFirst) {
            container.innerHTML = '';
        }

        // Render items in this chunk
        items.forEach(item => {
            const itemElement = this.createChangeItemElement(item);
            if (itemElement) {
                container.appendChild(itemElement);
            }
        });
    }

    createChangeItemElement(item) {
        // Create individual change item element
        const element = document.createElement('div');
        element.className = `change-item ${item.selected_for_report ? 'selected' : ''}`;
        element.dataset.id = item.id;

        element.innerHTML = `
            <div class="change-header">
                <div class="employee-info">
                    <strong>${item.employee_id} - ${item.employee_name}</strong>
                </div>
                <div class="change-badges">
                    <span class="badge change-type ${item.change_type.toLowerCase()}">${item.change_type}</span>
                    <span class="badge priority ${item.priority.toLowerCase()}">${item.priority}</span>
                </div>
            </div>
            <div class="change-details">
                <div class="detail-row">
                    <span class="label">Section:</span>
                    <span class="value">${item.section_name}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Item:</span>
                    <span class="value">${item.item_label}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Change:</span>
                    <span class="value">${item.previous_value || 'N/A'} → ${item.current_value || 'N/A'}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Category:</span>
                    <span class="value">${item.bulk_category}</span>
                </div>
            </div>
        `;

        // Add click handler
        element.addEventListener('click', () => {
            element.classList.toggle('selected');
            if (element.classList.contains('selected')) {
                this.selectedChanges.add(item.id);
            } else {
                this.selectedChanges.delete(item.id);
            }
            this.updateSelectionCount();
        });

        return element;
    }

    showSuccessMessage(message) {
        if (!this.container || !this.container.nodeType) {
            console.error('❌ Cannot show success message - invalid container:', this.container);
            console.log('Success message:', message);
            return;
        }
        this.container.innerHTML = `
            <div class="pre-reporting-success">
                <div class="success-icon">✅</div>
                <h3>Report Generation Complete!</h3>
                <p>${message}</p>
                <div class="success-actions">
                    <button class="btn primary" onclick="window.location.reload()">
                        Start New Audit
                    </button>
                    <button class="btn secondary" onclick="window.close()">
                        Close Application
                    </button>
                </div>
            </div>
        `;
    }

    // ENHANCEMENT: Generate Pre-Report (traditional functionality) with robustness
    generatePreReport() {
        console.log('📄 Generating Pre-Report...');

        const selectedCount = this.selectedChanges.size;
        if (selectedCount === 0) {
            this.showErrorMessage('Please select at least one change for the pre-report.');
            return;
        }

        // Prevent multiple simultaneous generations
        if (this.isProcessing) {
            console.log('⚠️ Pre-Report generation already in progress');
            return;
        }

        this.debounce('generatePreReport', async () => {
            await this.retryOperation('Pre-Report Generation', async () => {
                this.setLoadingState('preReport', true);
                this.isProcessing = true;

                try {
                    // Use existing report generation logic
                    await this.proceedToReportGeneration();
                } finally {
                    this.setLoadingState('preReport', false);
                    this.isProcessing = false;
                }
            });
        });
    }

    // ENHANCEMENT: Generate Final Report (new smart functionality) with robustness
    generateFinalReport() {
        console.log('📄 Generating Final Report with smart features...');

        const selectedCount = this.selectedChanges.size;
        if (selectedCount === 0) {
            this.showErrorMessage('Please select at least one change for the final report.');
            return;
        }

        // Validate configuration
        if (!this.finalReportConfig.generatedBy || !this.finalReportConfig.designation) {
            this.showErrorMessage('Please complete the report configuration (Generated By and Designation) before generating the final report.');
            return;
        }

        // Prevent multiple simultaneous generations
        if (this.isProcessing) {
            console.log('⚠️ Final Report generation already in progress');
            return;
        }

        this.debounce('generateFinalReport', async () => {
            await this.retryOperation('Final Report Generation', async () => {
                this.setLoadingState('finalReport', true);
                this.isProcessing = true;

                try {
                    console.log('🎯 Final Report Configuration:', this.finalReportConfig);
                    console.log('📊 Selected Changes:', Array.from(this.selectedChanges));

                    // Initialize Business Rules Engine if not already done
                    this.initializeBusinessRulesEngine();

                    // Apply business rules to selected changes
                    const selectedChangesArray = this.analyzedChanges.filter(change =>
                        this.selectedChanges.has(change.id)
                    );

                    const processedResults = this.businessRulesEngine.processChanges(
                        selectedChangesArray,
                        this.finalReportConfig
                    );

                    console.log('🎯 Business Rules Processing Results:', processedResults);

                    // Generate smart report with processed results
                    await this.generateSmartReport(processedResults);

                } finally {
                    this.setLoadingState('finalReport', false);
                    this.isProcessing = false;
                }
            });
        });
    }

    // Initialize Business Rules Engine and Smart Report Generator
    initializeBusinessRulesEngine() {
        if (!this.businessRulesEngine && window.BusinessRulesEngine) {
            this.businessRulesEngine = new window.BusinessRulesEngine();
            console.log('🎯 Business Rules Engine initialized');
        } else if (!window.BusinessRulesEngine) {
            console.warn('⚠️ Business Rules Engine not available, falling back to basic processing');
        }

        if (!this.smartReportGenerator && window.SmartReportGenerator) {
            this.smartReportGenerator = new window.SmartReportGenerator();
            console.log('📄 Smart Report Generator initialized');
        } else if (!window.SmartReportGenerator) {
            console.warn('⚠️ Smart Report Generator not available, falling back to basic reporting');
        }

        // Initialize Word Template Engine
        if (!this.wordTemplateEngine && window.WordTemplateEngine) {
            this.wordTemplateEngine = new window.WordTemplateEngine();
            console.log('📄 Word Template Engine initialized');
        } else if (!window.WordTemplateEngine) {
            console.warn('⚠️ Word Template Engine not available, Word generation will be disabled');
        }

        // Initialize PDF Template Engine
        if (!this.pdfTemplateEngine && window.PDFTemplateEngine) {
            this.pdfTemplateEngine = new window.PDFTemplateEngine();
            console.log('📄 PDF Template Engine initialized');
        } else if (!window.PDFTemplateEngine) {
            console.warn('⚠️ PDF Template Engine not available, PDF generation will be disabled');
        }

        // Initialize Excel Template Engine
        if (!this.excelTemplateEngine && window.ExcelTemplateEngine) {
            this.excelTemplateEngine = new window.ExcelTemplateEngine();
            console.log('📊 Excel Template Engine initialized');
        } else if (!window.ExcelTemplateEngine) {
            console.warn('⚠️ Excel Template Engine not available, Excel generation will be disabled');
        }
    }

    // Generate smart report with business rules processing
    async generateSmartReport(processedResults) {
        console.log('🎯 Generating smart report with business rules...');

        // Create enhanced report data structure
        const reportData = {
            metadata: {
                generatedBy: this.finalReportConfig.generatedBy,
                designation: this.finalReportConfig.designation,
                reportType: this.finalReportConfig.reportType,
                outputFormat: this.finalReportConfig.outputFormat,
                generatedAt: new Date().toISOString(),
                businessRulesApplied: true
            },
            summary: processedResults.summary,
            findings: {
                totalChanges: processedResults.categorized.length,
                highPriorityChanges: processedResults.categorized.filter(c => c.priority === 'HIGH'),
                moderatePriorityChanges: processedResults.categorized.filter(c => c.priority === 'MODERATE'),
                lowPriorityChanges: processedResults.categorized.filter(c => c.priority === 'LOW')
            },
            specialFindings: {
                promotions: processedResults.promotions,
                transfers: processedResults.transfers,
                bulkChanges: processedResults.bulkAnalysis
            },
            processedChanges: processedResults.categorized
        };

        console.log('📊 Smart Report Data:', reportData);

        // Generate smart report using Smart Report Generator
        if (this.smartReportGenerator) {
            const smartReport = this.smartReportGenerator.generateReport(reportData);
            console.log('📄 Smart Report Generated:', smartReport);

            // Show smart report preview to user
            this.showSmartReportPreview(smartReport);
        } else {
            console.warn('⚠️ Smart Report Generator not available, using basic report generation');
            // Save the enhanced report data to Report Manager directly
            await this.saveEnhancedReportToReportManager(reportData);
        }
    }

    // Show smart report preview to user
    showSmartReportPreview(smartReport) {
        console.log('📄 Showing smart report preview...');

        // Create preview modal or interface
        const previewHtml = `
            <div class="smart-report-preview">
                <div class="preview-header">
                    <h3>📄 Smart Report Preview</h3>
                    <p>Generated using AI-powered business rules analysis</p>
                </div>

                <div class="preview-content">
                    <div class="executive-summary">
                        <h4>${smartReport.executiveSummary.title}</h4>
                        <div class="key-metrics">
                            <span class="metric">📊 ${smartReport.executiveSummary.keyMetrics.totalChanges} Total Changes</span>
                            <span class="metric">🔴 ${smartReport.executiveSummary.keyMetrics.highPriorityChanges} High Priority</span>
                            <span class="metric">📈 ${smartReport.executiveSummary.keyMetrics.promotions} Promotions</span>
                            <span class="metric">🔄 ${smartReport.executiveSummary.keyMetrics.transfers} Transfers</span>
                        </div>
                        <div class="summary-text">
                            ${smartReport.executiveSummary.content.map(p => `<p>${p}</p>`).join('')}
                        </div>
                    </div>

                    <div class="sections-preview">
                        <h4>Report Sections</h4>
                        <ul>
                            ${smartReport.sections.map(section =>
                                `<li><strong>${section.title}</strong> ${section.count ? `(${section.count} items)` : ''}</li>`
                            ).join('')}
                        </ul>
                    </div>

                    ${smartReport.recommendations.length > 0 ? `
                        <div class="recommendations-preview">
                            <h4>Key Recommendations</h4>
                            <ul>
                                ${smartReport.recommendations.map(rec =>
                                    `<li><span class="priority-${rec.priority.toLowerCase()}">${rec.priority}</span> ${rec.title}</li>`
                                ).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>

                <div class="preview-actions">
                    <button class="btn secondary" onclick="window.interactivePreReporting.closeSmartReportPreview()">
                        📝 Edit Configuration
                    </button>

                    <div class="format-specific-actions" style="display: flex; gap: 10px; flex-wrap: wrap; margin: 10px 0;">
                        ${this.finalReportConfig.outputFormat === 'word' ? `
                            <button class="btn primary" onclick="window.interactivePreReporting.generateWordDocument()">
                                📄 Generate Word Document
                            </button>
                        ` : ''}

                        ${this.finalReportConfig.outputFormat === 'pdf' ? `
                            <button class="btn primary" onclick="window.interactivePreReporting.generatePDFDocument()">
                                📑 Generate PDF Document
                            </button>
                        ` : ''}

                        ${this.finalReportConfig.outputFormat === 'excel' ? `
                            <button class="btn primary" onclick="window.interactivePreReporting.generateExcelDocument()">
                                📊 Generate Excel Document
                            </button>
                        ` : ''}

                        <button class="btn secondary" onclick="window.interactivePreReporting.generateAllFormats()">
                            📦 Generate All Formats
                        </button>
                    </div>

                    <div class="primary-actions" style="display: flex; gap: 10px; margin-top: 15px;">
                        <button class="btn primary large" onclick="window.interactivePreReporting.generateFinalSmartReport()">
                            📊 Generate Full Report Suite
                        </button>
                        <button class="btn success" onclick="window.interactivePreReporting.saveCurrentSmartReportToManager()"
                                style="background: #28a745; border-color: #28a745;">
                            💾 Save to Report Manager
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Show preview in container or modal
        if (!this.container || !this.container.nodeType) {
            console.error('❌ Cannot show preview - invalid container:', this.container);
            return;
        }
        this.container.innerHTML = previewHtml;

        // Store smart report for final generation
        this.currentSmartReport = smartReport;
    }

    // Close smart report preview
    closeSmartReportPreview() {
        console.log('📄 Closing smart report preview...');
        // Return to previous view
        this.processAndRender();
    }

    // Generate Word document using template engine
    async generateWordDocument() {
        console.log('📄 Generating Word document using template engine...');

        // Validation checks
        if (!this.currentSmartReport) {
            console.error('❌ No smart report data available');
            this.showErrorMessage('No smart report data available. Please regenerate the report.');
            return;
        }

        if (!this.wordTemplateEngine) {
            console.error('❌ Word Template Engine not available');
            this.showErrorMessage('Word Template Engine not available. Please refresh the page and try again.');
            return;
        }

        // Validate smart report structure
        if (!this.currentSmartReport.metadata || !this.currentSmartReport.findings) {
            console.error('❌ Invalid smart report structure');
            this.showErrorMessage('Invalid report data structure. Please regenerate the report.');
            return;
        }

        try {
            // Show enhanced loading state with progress
            const generateBtn = this.container.querySelector('.preview-actions .btn.primary');
            if (generateBtn) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Initializing Word Template Engine...';
            }

            console.log('📄 Smart Report Data for Word Generation:', {
                metadata: this.currentSmartReport.metadata,
                findingsCount: this.currentSmartReport.findings?.totalChanges || 0,
                sectionsCount: this.currentSmartReport.sections?.length || 0
            });

            // Update progress
            if (generateBtn) {
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing Report Data...';
            }

            let wordDocument = null;

            // Try frontend Word Template Engine first
            if (this.wordTemplateEngine) {
                try {
                    wordDocument = await this.wordTemplateEngine.generateDownloadableWord(this.currentSmartReport);
                    console.log('✅ Word document generated using frontend template engine');
                } catch (frontendError) {
                    console.warn('⚠️ Frontend Word Template Engine failed, trying backend:', frontendError);
                    wordDocument = null;
                }
            }

            // Fallback to backend Word Template Engine
            if (!wordDocument && window.api && window.api.generateWordTemplateDocument) {
                try {
                    if (generateBtn) {
                        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Using Backend Template Engine...';
                    }

                    const backendResult = await window.api.generateWordTemplateDocument(this.currentSmartReport);

                    if (backendResult.success) {
                        wordDocument = {
                            url: backendResult.url,
                            filename: backendResult.filename,
                            size: backendResult.fileSize
                        };
                        console.log('✅ Word document generated using backend template engine');
                    } else {
                        throw new Error(backendResult.error || 'Backend generation failed');
                    }
                } catch (backendError) {
                    console.error('❌ Backend Word Template Engine also failed:', backendError);
                    throw new Error('Both frontend and backend Word generation failed: ' + backendError.message);
                }
            }

            if (!wordDocument) {
                throw new Error('No Word Template Engine available');
            }

            console.log('✅ Word document generated successfully:', {
                filename: wordDocument.filename,
                size: wordDocument.size,
                url: wordDocument.url ? 'Generated' : 'Not available',
                method: this.wordTemplateEngine ? 'Frontend Template Engine' : 'Backend Template Engine'
            });

            // Update progress
            if (generateBtn) {
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Preparing Download...';
            }

            // Create and trigger download
            const downloadLink = document.createElement('a');
            downloadLink.href = wordDocument.url;
            downloadLink.download = wordDocument.filename;
            downloadLink.style.display = 'none';

            // Add to DOM and trigger download
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            // Clean up the blob URL after a short delay (only for frontend generated URLs)
            if (this.wordTemplateEngine && wordDocument.url.startsWith('blob:')) {
                setTimeout(() => {
                    URL.revokeObjectURL(wordDocument.url);
                }, 1000);
            }

            // Show enhanced success message
            this.showSuccessMessage(`
                📄 Word document generated successfully!<br>
                <strong>File:</strong> ${wordDocument.filename}<br>
                <strong>Size:</strong> ${(wordDocument.size / 1024).toFixed(1)} KB<br>
                <strong>Format:</strong> Professional Word Document (.doc)
            `);

            // Reset button state
            if (generateBtn) {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '📄 Generate Word Document';
            }

            // Log success for analytics
            console.log('📊 Word Document Generation Analytics:', {
                reportType: this.finalReportConfig.reportType,
                selectedChanges: this.selectedChanges.size,
                generatedBy: this.finalReportConfig.generatedBy,
                fileSize: wordDocument.size,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('❌ Error generating Word document:', error);

            // Show detailed error message
            let errorMessage = 'Failed to generate Word document. ';
            if (error.message.includes('template')) {
                errorMessage += 'Template processing error. Please check your report data.';
            } else if (error.message.includes('data')) {
                errorMessage += 'Invalid report data. Please regenerate the smart report.';
            } else {
                errorMessage += error.message || 'Unknown error occurred.';
            }

            this.showErrorMessage(errorMessage);

            // Reset button state
            const generateBtn = this.container.querySelector('.preview-actions .btn.primary');
            if (generateBtn) {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '📄 Generate Word Document';
            }
        }
    }

    // Generate PDF document using PDF Template Engine
    async generatePDFDocument() {
        console.log('📑 Generating PDF document using template engine...');

        // Validation checks
        if (!this.currentSmartReport) {
            console.error('❌ No smart report data available');
            this.showErrorMessage('No smart report data available. Please regenerate the report.');
            return;
        }

        if (!this.pdfTemplateEngine) {
            console.error('❌ PDF Template Engine not available');
            this.showErrorMessage('PDF Template Engine not available. Please refresh the page and try again.');
            return;
        }

        try {
            // Show enhanced loading state with progress
            const generateBtn = this.container.querySelector('.format-specific-actions .btn.primary');
            if (generateBtn) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Initializing PDF Template Engine...';
            }

            console.log('📑 Smart Report Data for PDF Generation:', {
                metadata: this.currentSmartReport.metadata,
                findingsCount: this.currentSmartReport.findings?.totalChanges || 0,
                sectionsCount: this.currentSmartReport.sections?.length || 0
            });

            // Update progress
            if (generateBtn) {
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing Report Data...';
            }

            // Generate PDF document using template engine
            const pdfDocument = await this.pdfTemplateEngine.generatePDFDocument(this.currentSmartReport);

            console.log('✅ PDF document generated successfully:', {
                filename: pdfDocument.filename,
                size: pdfDocument.size,
                url: pdfDocument.url ? 'Generated' : 'Not available'
            });

            // Update progress
            if (generateBtn) {
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Preparing Download...';
            }

            // Create and trigger download
            const downloadLink = document.createElement('a');
            downloadLink.href = pdfDocument.url;
            downloadLink.download = pdfDocument.filename;
            downloadLink.style.display = 'none';

            // Add to DOM and trigger download
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            // Clean up the blob URL after a short delay
            setTimeout(() => {
                if (pdfDocument.url) {
                    URL.revokeObjectURL(pdfDocument.url);
                }
            }, 1000);

            // Show enhanced success message
            this.showSuccessMessage(`
                📑 PDF document generated successfully!<br>
                <strong>File:</strong> ${pdfDocument.filename}<br>
                <strong>Size:</strong> ${(pdfDocument.size / 1024).toFixed(1)} KB<br>
                <strong>Format:</strong> Professional PDF Document (.pdf)
            `);

            // Reset button state
            if (generateBtn) {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '📑 Generate PDF Document';
            }

            // Log success for analytics
            console.log('📊 PDF Document Generation Analytics:', {
                reportType: this.finalReportConfig.reportType,
                selectedChanges: this.selectedChanges.size,
                generatedBy: this.finalReportConfig.generatedBy,
                fileSize: pdfDocument.size,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('❌ Error generating PDF document:', error);

            // Show detailed error message
            let errorMessage = 'Failed to generate PDF document. ';
            if (error.message.includes('jsPDF')) {
                errorMessage += 'PDF library loading error. Please check your internet connection.';
            } else if (error.message.includes('template')) {
                errorMessage += 'Template processing error. Please check your report data.';
            } else if (error.message.includes('data')) {
                errorMessage += 'Invalid report data. Please regenerate the smart report.';
            } else {
                errorMessage += error.message || 'Unknown error occurred.';
            }

            this.showErrorMessage(errorMessage);

            // Reset button state
            const generateBtn = this.container.querySelector('.format-specific-actions .btn.primary');
            if (generateBtn) {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '📑 Generate PDF Document';
            }
        }
    }

    // Generate Excel document using Excel Template Engine
    async generateExcelDocument() {
        console.log('📊 Generating Excel document using template engine...');

        // Validation checks
        if (!this.currentSmartReport) {
            console.error('❌ No smart report data available');
            this.showErrorMessage('No smart report data available. Please regenerate the report.');
            return;
        }

        if (!this.excelTemplateEngine) {
            console.error('❌ Excel Template Engine not available');
            this.showErrorMessage('Excel Template Engine not available. Please refresh the page and try again.');
            return;
        }

        try {
            // Show enhanced loading state with progress
            const generateBtn = this.container.querySelector('.format-specific-actions .btn.primary');
            if (generateBtn) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Initializing Excel Template Engine...';
            }

            console.log('📊 Smart Report Data for Excel Generation:', {
                metadata: this.currentSmartReport.metadata,
                findingsCount: this.currentSmartReport.findings?.totalChanges || 0,
                sectionsCount: this.currentSmartReport.sections?.length || 0
            });

            // Update progress
            if (generateBtn) {
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing Report Data...';
            }

            // Generate Excel document using template engine
            const excelDocument = await this.excelTemplateEngine.generateExcelDocument(this.currentSmartReport);

            console.log('✅ Excel document generated successfully:', {
                filename: excelDocument.filename,
                size: excelDocument.size,
                url: excelDocument.url ? 'Generated' : 'Not available'
            });

            // Update progress
            if (generateBtn) {
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Preparing Download...';
            }

            // Create and trigger download
            const downloadLink = document.createElement('a');
            downloadLink.href = excelDocument.url;
            downloadLink.download = excelDocument.filename;
            downloadLink.style.display = 'none';

            // Add to DOM and trigger download
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            // Clean up the blob URL after a short delay
            setTimeout(() => {
                if (excelDocument.url) {
                    URL.revokeObjectURL(excelDocument.url);
                }
            }, 1000);

            // Show enhanced success message
            this.showSuccessMessage(`
                📊 Excel document generated successfully!<br>
                <strong>File:</strong> ${excelDocument.filename}<br>
                <strong>Size:</strong> ${(excelDocument.size / 1024).toFixed(1)} KB<br>
                <strong>Format:</strong> Professional Excel Workbook (.xlsx)<br>
                <strong>Sheets:</strong> Summary, Findings, Employee Analysis, Statistics
            `);

            // Reset button state
            if (generateBtn) {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '📊 Generate Excel Document';
            }

            // Log success for analytics
            console.log('📊 Excel Document Generation Analytics:', {
                reportType: this.finalReportConfig.reportType,
                selectedChanges: this.selectedChanges.size,
                generatedBy: this.finalReportConfig.generatedBy,
                fileSize: excelDocument.size,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('❌ Error generating Excel document:', error);

            // Show detailed error message
            let errorMessage = 'Failed to generate Excel document. ';
            if (error.message.includes('SheetJS') || error.message.includes('XLSX')) {
                errorMessage += 'Excel library loading error. Please check your internet connection.';
            } else if (error.message.includes('template')) {
                errorMessage += 'Template processing error. Please check your report data.';
            } else if (error.message.includes('data')) {
                errorMessage += 'Invalid report data. Please regenerate the smart report.';
            } else {
                errorMessage += error.message || 'Unknown error occurred.';
            }

            this.showErrorMessage(errorMessage);

            // Reset button state
            const generateBtn = this.container.querySelector('.format-specific-actions .btn.primary');
            if (generateBtn) {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '📊 Generate Excel Document';
            }
        }
    }

    // Generate all formats
    async generateAllFormats() {
        console.log('📦 Generating all formats...');

        if (!this.currentSmartReport) {
            this.showErrorMessage('No smart report data available. Please regenerate the report.');
            return;
        }

        try {
            // Show loading state
            const allFormatsBtn = this.container.querySelector('.format-specific-actions .btn.secondary');
            if (allFormatsBtn) {
                allFormatsBtn.disabled = true;
                allFormatsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating All Formats...';
            }

            let generatedFormats = [];

            // Generate Word document
            if (this.wordTemplateEngine) {
                try {
                    await this.generateWordDocument();
                    generatedFormats.push('Word');
                } catch (error) {
                    console.warn('⚠️ Word generation failed:', error);
                }
            }

            // Generate PDF document
            if (this.pdfTemplateEngine) {
                try {
                    await this.generatePDFDocument();
                    generatedFormats.push('PDF');
                } catch (error) {
                    console.warn('⚠️ PDF generation failed:', error);
                }
            }

            // Generate Excel document
            if (this.excelTemplateEngine) {
                try {
                    await this.generateExcelDocument();
                    generatedFormats.push('Excel');
                } catch (error) {
                    console.warn('⚠️ Excel generation failed:', error);
                }
            }

            // Show comprehensive success message
            if (generatedFormats.length > 0) {
                this.showSuccessMessage(`
                    📦 All documents generated successfully!<br>
                    <strong>Generated:</strong> ${generatedFormats.join(', ')} format(s)<br>
                    <strong>Total Files:</strong> ${generatedFormats.length} professional reports<br>
                    <strong>Note:</strong> All reports are available in the Report Manager.
                `);
            } else {
                this.showErrorMessage('No documents could be generated. Please check template engines availability.');
            }

            // Reset button state
            if (allFormatsBtn) {
                allFormatsBtn.disabled = false;
                allFormatsBtn.innerHTML = '📦 Generate All Formats';
            }

        } catch (error) {
            console.error('❌ Error generating all formats:', error);
            this.showErrorMessage('Error generating documents: ' + error.message);

            // Reset button state
            const allFormatsBtn = this.container.querySelector('.format-specific-actions .btn.secondary');
            if (allFormatsBtn) {
                allFormatsBtn.disabled = false;
                allFormatsBtn.innerHTML = '📦 Generate All Formats';
            }
        }
    }

    // Generate final smart report
    generateFinalSmartReport() {
        console.log('📄 Generating final smart report...');

        if (this.currentSmartReport) {
            // Proceed with existing report generation using smart report data
            this.proceedToReportGeneration(this.currentSmartReport);
        } else {
            console.error('❌ No smart report data available');
            this.showErrorMessage('No smart report data available. Please try again.');
        }
    }

    showSuccessMessage(message) {
        // Show success message with enhanced styling
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.innerHTML = `
            <div class="alert alert-success" style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 8px; margin-bottom: 20px; position: relative;">
                <div style="display: flex; align-items: flex-start; gap: 10px;">
                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 1.2em; margin-top: 2px;"></i>
                    <div style="flex: 1;">
                        <strong>Success!</strong><br>
                        ${message}
                    </div>
                    <button type="button" class="close" onclick="this.parentElement.parentElement.remove()"
                            style="background: none; border: none; font-size: 1.5em; color: #155724; cursor: pointer; padding: 0; margin-left: 10px;">×</button>
                </div>
            </div>
        `;

        this.container.insertBefore(successDiv, this.container.firstChild);

        // Auto-remove after 8 seconds (longer for detailed messages)
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 8000);
    }

    showErrorMessage(message) {
        // Reset any generate buttons
        const generateBtn = this.container.querySelector('.pre-reporting-actions .btn.primary');
        if (generateBtn) {
            generateBtn.disabled = false;
            generateBtn.textContent = `Generate Final Report (${this.selectedChanges.size} changes)`;
        }

        // Show enhanced error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <div class="alert alert-danger" style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px; position: relative;">
                <div style="display: flex; align-items: flex-start; gap: 10px;">
                    <i class="fas fa-exclamation-triangle" style="color: #dc3545; font-size: 1.2em; margin-top: 2px;"></i>
                    <div style="flex: 1;">
                        <strong>Error!</strong><br>
                        ${message}
                    </div>
                    <button type="button" class="close" onclick="this.parentElement.parentElement.remove()"
                            style="background: none; border: none; font-size: 1.5em; color: #721c24; cursor: pointer; padding: 0; margin-left: 10px;">×</button>
                </div>
            </div>
        `;

        this.container.insertBefore(errorDiv, this.container.firstChild);

        // Auto-remove after 8 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 8000);
    }

    getSelectedChanges() {
        return Array.from(this.selectedChanges);
    }
}

// Make the class globally available
window.InteractivePreReporting = InteractivePreReporting;

// PRODUCTION FIX: Create a global placeholder object to prevent onclick handler errors
// This ensures that HTML onclick handlers don't fail before the class is instantiated
if (!window.interactivePreReporting) {
    window.interactivePreReporting = {
        // Placeholder functions that will be replaced when the class is instantiated
        toggleChangeDetails: () => console.warn('InteractivePreReporting not yet initialized'),
        showAllChanges: () => console.warn('InteractivePreReporting not yet initialized'),
        toggleChange: () => console.warn('InteractivePreReporting not yet initialized'),
        generateFinalReport: () => console.warn('InteractivePreReporting not yet initialized'),
        generatePreReport: () => console.warn('InteractivePreReporting not yet initialized'),
        togglePriorityGroup: () => console.warn('InteractivePreReporting not yet initialized'),
        toggleEmployeeGroup: () => console.warn('InteractivePreReporting not yet initialized'),
        toggleFlagGroup: () => console.warn('InteractivePreReporting not yet initialized'),
        handleSearch: () => console.warn('InteractivePreReporting not yet initialized'),
        handleSearchDebounced: () => console.warn('InteractivePreReporting not yet initialized'),
        clearSearch: () => console.warn('InteractivePreReporting not yet initialized'),
        loadDataFromDatabase: () => console.warn('InteractivePreReporting not yet initialized'),
        closeSmartReportPreview: () => console.warn('InteractivePreReporting not yet initialized'),
        generateWordDocument: () => console.warn('InteractivePreReporting not yet initialized'),
        generatePDFDocument: () => console.warn('InteractivePreReporting not yet initialized'),
        generateExcelDocument: () => console.warn('InteractivePreReporting not yet initialized'),
        generateAllFormats: () => console.warn('InteractivePreReporting not yet initialized'),
        generateFinalSmartReport: () => console.warn('InteractivePreReporting not yet initialized'),
        saveCurrentSmartReportToManager: () => console.warn('InteractivePreReporting not yet initialized')
    };
    console.log('✅ Global placeholder functions created for InteractivePreReporting');
}
