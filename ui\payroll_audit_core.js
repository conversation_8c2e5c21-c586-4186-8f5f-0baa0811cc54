/**
 * payroll_audit_core.js
 * Core functionality for the payroll audit process
 */

// Core payroll audit functionality
// 🎯 CORRECTED: Use global window properties instead of redeclaring variables
// Variables are already declared in renderer.js to prevent conflicts
// let auditCurrentPdfPath = null;     // ← REMOVED: Already declared in renderer.js
// let auditPreviousPdfPath = null;    // ← REMOVED: Already declared in renderer.js
// let enhancedProcessActive = false;  // ← REMOVED: Already declared in renderer.js
// let processStartTime = null;        // ← REMOVED: Already declared in renderer.js

/**
 * Start the payroll audit process with selected files
 */
async function startPayrollAuditProcess() {
  console.log('🚀🚀🚀 START PAYROLL AUDIT PROCESS CALLED! 🚀🚀🚀');

  try {
    // Get file paths from global variables
    const auditCurrentPdfPath = window.auditCurrentPdfPath;
    const auditPreviousPdfPath = window.auditPreviousPdfPath;

    console.log('Current PDF:', auditCurrentPdfPath);
    console.log('Previous PDF:', auditPreviousPdfPath);

    if (!auditCurrentPdfPath || !auditPreviousPdfPath) {
      alert('Please select both current and previous payroll files');
      return Promise.reject(new Error('Missing file paths'));
    }

    // Configuration now handled in Final Report Interface - no validation needed here

    // Get selected months and years
    const currentMonth = document.getElementById('current-month')?.value || '';
    const currentYear = document.getElementById('current-year')?.value || '';
    const previousMonth = document.getElementById('previous-month')?.value || '';
    const previousYear = document.getElementById('previous-year')?.value || '';

    console.log('Starting backend processing...');

    // CRITICAL: Set process start time for timer
    processStartTime = Date.now();
    enhancedProcessActive = true;

    // 🎯 CORRECTED: Transform UI to processing view without undefined signature variables
    if (typeof window.transformToProcessingView === 'function') {
      window.transformToProcessingView(currentMonth, currentYear, previousMonth, previousYear);
    }

    // 🎯 CORRECTED: Hide start button using correct ID
    const startBtn = document.getElementById('start-payroll-audit');
    if (startBtn) {
      startBtn.style.display = 'none';
    }

    // Start processing with NEW phased process manager workflow
    const auditResult = await window.api.enhancedPayrollAudit(
      auditCurrentPdfPath,
      auditPreviousPdfPath,
      {
        currentMonth,
        currentYear,
        previousMonth,
        previousYear
      }
    );

    console.log('🎉 Phased workflow completed:', auditResult);

    // 🎯 CORRECTED: Capture workflow session to prevent bulletproof architecture from using wrong session
    if (auditResult && (auditResult.sessionId || auditResult.session_id)) {
      const sessionId = auditResult.sessionId || auditResult.session_id;
      window.currentWorkflowSession = sessionId;
      console.log(`🎯 CAPTURED WORKFLOW SESSION: ${sessionId}`);
    }

    // Use the new workflow completion handler
    if (window.handlePhasedWorkflowCompletion) {
      window.handlePhasedWorkflowCompletion(auditResult);
    }

    // Handle specific error cases
    if (auditResult && !auditResult.success) {
      console.error('❌ Workflow failed:', auditResult.error);
      alert(`Audit process failed: ${auditResult.error}`);

      // Reset UI state
      enhancedProcessActive = false;
      if (startBtn) {
        startBtn.style.display = 'inline-block';
      }
      return Promise.reject(new Error(auditResult.error));
    }

    // Initialize progress listener
    if (window.initializeProgressListener) {
      window.initializeProgressListener();
    }

    console.log('✅ Audit process completed successfully');
    return auditResult;

  } catch (error) {
    console.error('Error starting payroll audit:', error);
    alert('Error starting payroll audit. Please check the console for details.');

    // Reset UI state
    enhancedProcessActive = false;
    const startBtn = document.getElementById('start-payroll-audit');
    if (startBtn) {
      startBtn.style.display = 'inline-block';
    }

    if (window.restoreAuditButtons) {
      window.restoreAuditButtons();
    }

    return Promise.reject(error);
  }
}

/**
 * Stop the payroll audit process
 */
async function stopPayrollAuditProcess() {
  console.log('⛔ STOPPING PAYROLL AUDIT PROCESS');

  try {
    // Call new phased process stop API
    if (window.api.stopProcess) {
      const result = await window.api.stopProcess();
      if (result && result.success) {
        console.log('✅ New phased process stopped successfully');
      }
    }

    // Also call legacy stop API for compatibility
    if (window.api.stopPayrollAudit) {
      window.api.stopPayrollAudit();
    }

    // Update UI
    const stopBtn = document.getElementById('stop-audit-button');
    if (stopBtn) {
      stopBtn.disabled = true;
      stopBtn.textContent = 'Stopping...';
    }

    window.addRealTimeActivity('stopping', 'Stopping process...', null, 'warning');

    // Reset processing variables
    enhancedProcessActive = false;

    // Stop timers
    window.stopProcessingTimer();

    setTimeout(() => {
      window.restoreOriginalView();
      window.restoreAuditButtons();
    }, 1000);

  } catch (error) {
    console.error('Error stopping payroll audit:', error);
    alert('Error stopping payroll audit process.');
    window.restoreAuditButtons();
  }
}

/**
 * Pause the payroll audit process
 */
async function pausePayrollAuditProcess() {
  console.log('⏸️ PAUSING PAYROLL AUDIT PROCESS');

  try {
    const result = await window.api.pauseProcess();

    if (result && result.success) {
      console.log('✅ Process paused successfully');

      // Update UI
      const pauseBtn = document.getElementById('pause-audit-button');
      const resumeBtn = document.getElementById('resume-audit-button');

      if (pauseBtn) pauseBtn.classList.add('hidden');
      if (resumeBtn) resumeBtn.classList.remove('hidden');

      window.addRealTimeActivity('paused', 'Process paused by user', null, 'info');
    } else {
      console.error('❌ Failed to pause process:', result?.error);
    }

  } catch (error) {
    console.error('Error pausing payroll audit:', error);
  }
}

/**
 * Resume the payroll audit process
 */
async function resumePayrollAuditProcess() {
  console.log('▶️ RESUMING PAYROLL AUDIT PROCESS');

  try {
    const result = await window.api.resumeProcess();

    if (result && result.success) {
      console.log('✅ Process resumed successfully');

      // Update UI
      const pauseBtn = document.getElementById('pause-audit-button');
      const resumeBtn = document.getElementById('resume-audit-button');

      if (pauseBtn) pauseBtn.classList.remove('hidden');
      if (resumeBtn) resumeBtn.classList.add('hidden');

      window.addRealTimeActivity('resumed', 'Process resumed by user', null, 'success');
    } else {
      console.error('❌ Failed to resume process:', result?.error);
    }

  } catch (error) {
    console.error('Error resuming payroll audit:', error);
  }
}

/**
 * Restore audit buttons to original state
 */
function restoreAuditButtons() {
  const startBtn = document.getElementById('start-audit-button');
  const stopBtn = document.getElementById('stop-audit-button');
  
  if (startBtn) startBtn.classList.remove('hidden');
  if (stopBtn) {
    stopBtn.classList.add('hidden');
    stopBtn.disabled = false;
    stopBtn.textContent = 'Stop Process';
  }
}

/**
 * Restore original view
 */
function restoreOriginalView() {
  const processingView = document.getElementById('payroll-processing-view');
  const selectionView = document.getElementById('payroll-selection-view');
  
  if (processingView) processingView.classList.add('hidden');
  if (selectionView) selectionView.classList.remove('hidden');
  
  enhancedProcessActive = false;
}

/**
 * Handle audit file selection
 */
function handleAuditFileSelection(filePath, type) {
  if (!filePath) return;

  console.log(`🔄 Handling file selection: ${type} = ${filePath}`);

  if (type === 'current') {
    window.auditCurrentPdfPath = filePath;
    // 🎯 ENHANCED: Also update the local variables in renderer.js
    if (typeof auditCurrentPdfPath !== 'undefined') {
      auditCurrentPdfPath = filePath;
    }
    // 🎯 CORRECTED: Use correct element IDs from HTML
    window.updateAuditFileInfo('current-payroll-info', filePath);
  } else if (type === 'previous') {
    window.auditPreviousPdfPath = filePath;
    // 🎯 ENHANCED: Also update the local variables in renderer.js
    if (typeof auditPreviousPdfPath !== 'undefined') {
      auditPreviousPdfPath = filePath;
    }
    // 🎯 CORRECTED: Use correct element IDs from HTML
    window.updateAuditFileInfo('previous-payroll-info', filePath);
  }

  // Check if both files are selected to enable the start button
  window.checkAuditButtonState();
}

/**
 * Select audit file via dialog with retry mechanism
 */
async function selectAuditFile(type, retryCount = 0) {
  const maxRetries = 2;

  try {
    console.log(`📂 Opening file dialog for ${type} payroll... (attempt ${retryCount + 1})`);

    // Show loading indicator for user feedback
    const loadingMessage = document.createElement('div');
    loadingMessage.id = 'file-dialog-loading';
    loadingMessage.innerHTML = `
      <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                  background: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                  z-index: 10000; text-align: center;">
        <div style="margin-bottom: 10px;">🔄 Opening file dialog...</div>
        <div style="font-size: 12px; color: #666;">Please wait, this may take a moment</div>
      </div>
    `;
    document.body.appendChild(loadingMessage);

    // 🎯 ENHANCED: Use available API functions with better error handling
    let filePath = null;

    try {
      // Try the simple selectPdfFile API first
      if (window.api && window.api.selectPdfFile) {
        filePath = await window.api.selectPdfFile();
      }
      // Fallback to electronAPI showOpenDialog
      else if (window.electronAPI && window.electronAPI.showOpenDialog) {
        const result = await window.electronAPI.showOpenDialog({
          title: type === 'current' ? 'Select Current Payroll PDF' : 'Select Previous Payroll PDF',
          filters: [
            { name: 'PDF Files', extensions: ['pdf'] },
            { name: 'All Files', extensions: ['*'] }
          ],
          properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
          filePath = result.filePaths[0];
        }
      }
      else {
        throw new Error('No file dialog API available');
      }
    } finally {
      // Remove loading indicator
      const loading = document.getElementById('file-dialog-loading');
      if (loading) {
        loading.remove();
      }
    }

    if (filePath) {
      console.log(`✅ Selected file for ${type}: ${filePath}`);
      window.handleAuditFileSelection(filePath, type);

      // Auto-set month and year based on filename
      const filename = filePath.split('\\').pop().split('/').pop();
      const match = filename.match(/(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i);
      if (match) {
        const monthElement = document.getElementById(type === 'current' ? 'current-month' : 'previous-month');
        if (monthElement) {
          monthElement.value = match[0].toUpperCase();
        }
      }

      const yearMatch = filename.match(/20\d{2}/);
      if (yearMatch) {
        const yearElement = document.getElementById(type === 'current' ? 'current-year' : 'previous-year');
        if (yearElement) {
          yearElement.value = yearMatch[0];
        }
      }
    } else {
      console.log(`ℹ️ No file selected for ${type}`);
    }
  } catch (error) {
    console.error('Error selecting file:', error);

    // Remove loading indicator if still present
    const loading = document.getElementById('file-dialog-loading');
    if (loading) {
      loading.remove();
    }

    // Retry logic for timeout errors
    if (error.message.includes('timeout') && retryCount < maxRetries) {
      console.log(`🔄 Retrying file dialog (attempt ${retryCount + 2}/${maxRetries + 1})...`);

      // Wait a moment before retrying
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Retry the file selection
      return selectAuditFile(type, retryCount + 1);
    }

    // Provide user-friendly error messages
    let userMessage = 'Error selecting file: ';

    if (error.message.includes('timeout')) {
      userMessage += `The file dialog timed out after ${maxRetries + 1} attempts. This can happen if:\n` +
                    '• Your system is running slowly\n' +
                    '• Antivirus software is interfering\n' +
                    '• Windows dialog system is busy\n\n' +
                    'Solutions:\n' +
                    '• Restart the application\n' +
                    '• Temporarily disable antivirus\n' +
                    '• Close other applications to free up system resources';
    } else if (error.message.includes('permission')) {
      userMessage += 'Permission denied. Please:\n' +
                    '• Run the application as administrator\n' +
                    '• Check file/folder permissions\n' +
                    '• Ensure the file is not locked by another program';
    } else if (error.message.includes('No file dialog API available')) {
      userMessage += 'File dialog system not available. Please restart the application.';
    } else {
      userMessage += error.message;
    }

    alert(userMessage);
  }
}

/**
 * Update audit file info in the UI
 */
function updateAuditFileInfo(elementId, filePath) {
  console.log(`🎨 Updating UI element: ${elementId} with file: ${filePath}`);

  const element = document.getElementById(elementId);
  if (element) {
    const filename = filePath.split('\\').pop().split('/').pop();

    // 🎯 ENHANCED: Create rich file info display with green success indicator (filename only)
    element.innerHTML = `
      <div class="file-selected-info">
        <i class="fas fa-check-circle" style="color: #28a745; margin-right: 8px;"></i>
        <span class="file-name" style="color: #28a745; font-weight: 500;">${filename}</span>
      </div>
    `;

    element.title = filePath;
    element.style.display = 'block';
    element.parentElement.classList.remove('hidden');

    console.log(`✅ UI updated successfully for ${elementId}`);
  } else {
    console.error(`❌ Element not found: ${elementId}`);
  }
}

/**
 * Check if both files are selected to enable/disable the start button
 */
function checkAuditButtonState() {
  // 🎯 CORRECTED: Use correct button ID from HTML
  const startButton = document.getElementById('start-payroll-audit');
  if (!startButton) {
    console.error('❌ Start button not found: start-payroll-audit');
    return;
  }

  // 🎯 ENHANCED: Check both window and local variables for compatibility
  const currentFile = window.auditCurrentPdfPath || (typeof auditCurrentPdfPath !== 'undefined' ? auditCurrentPdfPath : null);
  const previousFile = window.auditPreviousPdfPath || (typeof auditPreviousPdfPath !== 'undefined' ? auditPreviousPdfPath : null);

  const bothFilesSelected = currentFile && previousFile;
  console.log(`🔍 Button state check: current=${!!currentFile}, previous=${!!previousFile}`);

  const canStart = bothFilesSelected;

  startButton.disabled = !canStart;

  // 🎯 ENHANCED: Add visual feedback for button state
  if (canStart) {
    startButton.classList.add('ready');
    startButton.style.background = '#28a745';
    startButton.style.color = 'white';
    startButton.style.cursor = 'pointer';
    startButton.style.opacity = '1';
    console.log('✅ Start button enabled and ready');
  } else {
    startButton.classList.remove('ready');
    startButton.style.background = '#6c757d';
    startButton.style.color = '#ffffff';
    startButton.style.cursor = 'not-allowed';
    startButton.style.opacity = '0.6';
    console.log('⚠️ Start button disabled - waiting for files');
  }

  // Update user guide text
  if (bothFilesSelected) {
    window.removeUserGuideText();
  }
}

/**
 * Remove user guide text when files are selected
 */
function removeUserGuideText() {
  const guideText = document.getElementById('guide-text');
  if (guideText) {
    guideText.classList.add('fade-out');
    setTimeout(() => {
      guideText.remove();
    }, 500);
  }
}

// Export functions to be available globally
window.startPayrollAuditProcess = startPayrollAuditProcess;
window.stopPayrollAuditProcess = stopPayrollAuditProcess;
window.pausePayrollAuditProcess = pausePayrollAuditProcess;
window.resumePayrollAuditProcess = resumePayrollAuditProcess;
window.restoreAuditButtons = restoreAuditButtons;
window.restoreOriginalView = restoreOriginalView;
window.handleAuditFileSelection = handleAuditFileSelection;
window.selectAuditFile = selectAuditFile;
window.updateAuditFileInfo = updateAuditFileInfo;
window.checkAuditButtonState = checkAuditButtonState;
window.removeUserGuideText = removeUserGuideText;
// 🎯 CORRECTED: Variables are managed by renderer.js, no need to reassign
// window.auditCurrentPdfPath and window.auditPreviousPdfPath are already set in renderer.js
