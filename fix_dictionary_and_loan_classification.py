#!/usr/bin/env python3
"""
Fix Dictionary and Loan Classification Issues
"""

import sqlite3
import json
import os
from datetime import datetime

def fix_dictionary_population():
    """Fix the empty dictionary by populating it from database or creating default structure"""
    print("🔧 FIXING DICTIONARY POPULATION")
    print("=" * 50)
    
    dict_path = 'core/payroll_dictionary.json'
    
    # Check if database has dictionary data
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Check if dictionary tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'dictionary%'")
        dict_tables = cursor.fetchall()
        
        print(f"Dictionary tables found: {[table[0] for table in dict_tables]}")
        
        if dict_tables:
            # Try to load from database
            print("📋 Loading dictionary from database...")
            
            # Get sections
            cursor.execute("SELECT id, section_name FROM dictionary_sections")
            sections = cursor.fetchall()
            
            dictionary = {}
            
            for section_id, section_name in sections:
                print(f"   Processing section: {section_name}")
                
                # Get items for this section
                cursor.execute('''
                    SELECT item_name, value_format, validation_rules, 
                           include_new, include_increase, include_decrease,
                           is_fixed
                    FROM dictionary_items 
                    WHERE section_id = ?
                ''', (section_id,))
                
                items = cursor.fetchall()
                
                section_items = {}
                for item_name, value_format, validation_rules, include_new, include_increase, include_decrease, is_fixed in items:
                    section_items[item_name] = {
                        'value_format': value_format or 'text',
                        'validation_rules': validation_rules or '',
                        'include_new': bool(include_new),
                        'include_increase': bool(include_increase), 
                        'include_decrease': bool(include_decrease),
                        'is_fixed': bool(is_fixed),
                        'is_column_header': False  # Default
                    }
                
                dictionary[section_name] = {
                    'items': section_items
                }
                
                print(f"     Added {len(section_items)} items")
            
            # Save to file
            with open(dict_path, 'w') as f:
                json.dump(dictionary, f, indent=2)
            
            print(f"✅ Dictionary populated with {len(dictionary)} sections")
            
        else:
            print("⚠️ No dictionary tables found in database")
            # Create basic structure with some common items
            create_basic_dictionary(dict_path)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error loading from database: {e}")
        # Fallback to creating basic structure
        create_basic_dictionary(dict_path)

def create_basic_dictionary(dict_path):
    """Create a basic dictionary structure with common items"""
    print("🏗️ Creating basic dictionary structure...")
    
    basic_dictionary = {
        "PERSONAL DETAILS": {
            "items": {
                "Employee No.": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True},
                "Employee Name": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True},
                "Department": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True},
                "Job Title": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True}
            }
        },
        "EARNINGS": {
            "items": {
                "BASIC SALARY": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True},
                "LEAVE ALLOWANCE": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True},
                "1ST FUEL ELEMENT": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True},
                "2ND FUEL ELEMENT": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True}
            }
        },
        "DEDUCTIONS": {
            "items": {
                "INCOME TAX": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True},
                "SSF EEMPLOYEE": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True},
                "PROVIDENT FUND": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True}
            }
        },
        "EMPLOYERS CONTRIBUTION": {
            "items": {
                "SSF EMPLOYER": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True}
            }
        },
        "LOANS": {
            "items": {
                "RENT ADVANCE": {"include_new": True, "include_increase": True, "include_decrease": False, "is_fixed": True},
                "SALARY ADVANCE": {"include_new": True, "include_increase": True, "include_decrease": False, "is_fixed": True},
                "SALARY ADVANCE PENT.": {"include_new": True, "include_increase": True, "include_decrease": False, "is_fixed": True},
                "BUILDING LOAN": {"include_new": True, "include_increase": True, "include_decrease": False, "is_fixed": True}
            }
        },
        "EMPLOYEE BANK DETAILS": {
            "items": {
                "Bank": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True},
                "Account No.": {"include_new": True, "include_increase": True, "include_decrease": True, "is_fixed": True}
            }
        }
    }
    
    with open(dict_path, 'w') as f:
        json.dump(basic_dictionary, f, indent=2)
    
    print(f"✅ Basic dictionary created with {len(basic_dictionary)} sections")

def fix_dictionary_manager_method():
    """Add the missing get_dictionary method to PayrollDictionaryManager"""
    print(f"\n🔧 FIXING DICTIONARY MANAGER METHOD")
    print("=" * 50)
    
    dict_manager_path = 'core/dictionary_manager.py'
    
    # Check if get_dictionary method exists
    with open(dict_manager_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if 'def get_dictionary(' in content:
        print("✅ get_dictionary method already exists")
        return True
    
    # Add the method
    method_code = '''
    def get_dictionary(self) -> Dict:
        """
        Get the current dictionary.
        
        Returns:
            The dictionary data structure
        """
        return self.dictionary
'''
    
    # Find a good place to insert it (after load_dictionary method)
    lines = content.split('\n')
    insert_index = -1
    
    for i, line in enumerate(lines):
        if 'def load_dictionary(' in line:
            # Find the end of this method
            indent_level = len(line) - len(line.lstrip())
            for j in range(i + 1, len(lines)):
                if lines[j].strip() == '':
                    continue
                current_indent = len(lines[j]) - len(lines[j].lstrip())
                if current_indent <= indent_level and lines[j].strip():
                    insert_index = j
                    break
            break
    
    if insert_index > 0:
        # Insert the method
        lines.insert(insert_index, method_code)
        
        # Write back to file
        with open(dict_manager_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("✅ get_dictionary method added to PayrollDictionaryManager")
        return True
    else:
        print("❌ Could not find insertion point for get_dictionary method")
        return False

def test_fixes():
    """Test if the fixes work"""
    print(f"\n🧪 TESTING FIXES")
    print("=" * 50)
    
    try:
        # Test dictionary loading
        dict_path = 'core/payroll_dictionary.json'
        with open(dict_path, 'r') as f:
            dictionary = json.load(f)
        
        total_items = sum(len(section.get('items', {})) for section in dictionary.values() if isinstance(section, dict))
        print(f"✅ Dictionary loaded: {len(dictionary)} sections, {total_items} items")
        
        # Test SALARY ADVANCE PENT classification
        from core.dictionary_manager import PayrollDictionaryManager
        
        dict_manager = PayrollDictionaryManager(debug=False)
        
        test_loan = "SALARY ADVANCE PENT."
        classification = dict_manager.classify_loan_type(test_loan)
        
        print(f"🏷️ {test_loan}: {classification}")
        
        if "IN-HOUSE" in classification:
            print("✅ SALARY ADVANCE PENT correctly classified as IN-HOUSE")
            return True
        else:
            print("❌ SALARY ADVANCE PENT still classified as EXTERNAL")
            
            # Check if it's in the dictionary
            loans_items = dictionary.get('LOANS', {}).get('items', {})
            if test_loan in loans_items:
                print(f"   ✅ Found in dictionary: {loans_items[test_loan]}")
            else:
                print(f"   ❌ Not found in dictionary")
            
            return False
            
    except Exception as e:
        print(f"❌ Error testing fixes: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Fixing dictionary and loan classification issues...")
    
    # Fix 1: Populate empty dictionary
    fix_dictionary_population()
    
    # Fix 2: Add missing method
    method_success = fix_dictionary_manager_method()
    
    # Test the fixes
    test_success = test_fixes()
    
    print(f'\n🎯 RESULTS:')
    print(f'   Dictionary population: ✅ COMPLETE')
    print(f'   Method addition: {"✅ SUCCESS" if method_success else "❌ FAILED"}')
    print(f'   Classification test: {"✅ WORKING" if test_success else "❌ STILL BROKEN"}')
    
    if test_success:
        print(f'\n🎉 ALL FIXES SUCCESSFUL!')
        print(f'   - Dictionary is now populated with items and toggle states')
        print(f'   - SALARY ADVANCE PENT is correctly classified as IN-HOUSE')
        print(f'   - Toggle functionality should now work properly')
    else:
        print(f'\n⚠️ Some issues remain - may need manual intervention')
