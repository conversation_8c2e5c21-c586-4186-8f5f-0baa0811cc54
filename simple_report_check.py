#!/usr/bin/env python3
"""
Simple Report Generation Check
"""

import os
import sqlite3

def simple_report_check():
    print("🔍 SIMPLE REPORT GENERATION CHECK")
    print("=" * 50)
    
    # Check 1: Reports directory
    print(f"📁 Reports directory:")
    if os.path.exists('reports'):
        files = os.listdir('reports')
        print(f"   ✅ Exists with {len(files)} files")
        
        if files:
            print(f"   📄 Recent files:")
            for file in files[-5:]:  # Last 5 files
                file_path = os.path.join('reports', file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"      - {file} ({size:,} bytes)")
        else:
            print(f"   ❌ Directory is empty")
    else:
        print(f"   ❌ Does not exist")
    
    # Check 2: Database reports
    print(f"\n🗄️ Database reports:")
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM reports')
        count = cursor.fetchone()[0]
        print(f"   📊 Total reports: {count}")
        
        if count > 0:
            cursor.execute('''
                SELECT report_id, title, file_paths 
                FROM reports 
                ORDER BY created_at DESC 
                LIMIT 3
            ''')
            
            reports = cursor.fetchall()
            print(f"   📋 Recent reports:")
            for report_id, title, file_paths in reports:
                print(f"      - {report_id}: {title}")
                print(f"        Paths: {file_paths}")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
    
    # Check 3: Key insight from terminal
    print(f"\n💡 KEY INSIGHT FROM TERMINAL:")
    print(f"   🔍 Terminal shows: 'word file exists: false'")
    print(f"   🔍 Terminal shows: 'pdf file exists: false'")
    print(f"   📋 This means:")
    print(f"      - Reports are saved to database ✅")
    print(f"      - File paths are stored ✅") 
    print(f"      - But actual files are NOT created ❌")
    
    print(f"\n🎯 ROOT CAUSE IDENTIFIED:")
    print(f"   The system saves report metadata to database")
    print(f"   But the actual Word/PDF files are never generated")
    print(f"   This is why downloads fail - no files exist!")

if __name__ == "__main__":
    simple_report_check()
