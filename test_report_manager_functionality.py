#!/usr/bin/env python3
"""
Test Report Manager Functionality - Comprehensive testing of report generation and saving
"""

import sqlite3
import json
import os
from datetime import datetime
import uuid

def test_report_manager_save():
    """Test saving a report to Report Manager"""
    print("🧪 TESTING REPORT MANAGER SAVE FUNCTIONALITY")
    print("=" * 60)
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Create a test report
    test_report = {
        'report_id': f'test_report_{int(datetime.now().timestamp())}',
        'report_type': 'PAYROLL_AUDIT_REPORT',
        'report_category': 'Interactive Pre-Reporting',
        'title': 'Test Payroll Audit Report',
        'description': f'Test report generated on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
        'file_paths': json.dumps({
            'word': 'reports/test_report.docx',
            'pdf': 'reports/test_report.pdf'
        }),
        'metadata': json.dumps({
            'generated_by': 'Test System',
            'designation': 'Test Auditor',
            'total_changes': 150,
            'selected_changes': 75,
            'generation_method': 'test_suite'
        }),
        'file_size': 1024000,  # 1MB test size
        'is_archived': False
    }
    
    try:
        # Insert test report
        cursor.execute('''
            INSERT INTO reports
            (report_id, report_type, report_category, title, description, file_paths, metadata, file_size, is_archived)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            test_report['report_id'],
            test_report['report_type'],
            test_report['report_category'],
            test_report['title'],
            test_report['description'],
            test_report['file_paths'],
            test_report['metadata'],
            test_report['file_size'],
            test_report['is_archived']
        ))
        
        conn.commit()
        
        # Verify the report was saved
        cursor.execute('SELECT * FROM reports WHERE report_id = ?', (test_report['report_id'],))
        saved_report = cursor.fetchone()
        
        if saved_report:
            print(f'✅ Test report saved successfully')
            print(f'   Report ID: {test_report["report_id"]}')
            print(f'   Type: {test_report["report_type"]}')
            print(f'   Category: {test_report["report_category"]}')
            print(f'   Title: {test_report["title"]}')
            
            # Test metadata parsing
            try:
                metadata = json.loads(saved_report[7])  # metadata column
                print(f'   Metadata: {metadata}')
                print(f'✅ Metadata parsing successful')
            except Exception as e:
                print(f'❌ Metadata parsing failed: {e}')
            
            return True
        else:
            print(f'❌ Test report not found after saving')
            return False
            
    except Exception as e:
        print(f'❌ Error saving test report: {e}')
        return False
    finally:
        conn.close()

def test_report_retrieval():
    """Test retrieving reports from Report Manager"""
    print(f'\n🔍 TESTING REPORT RETRIEVAL FUNCTIONALITY')
    print("=" * 60)
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    try:
        # Test getting all reports
        cursor.execute('''
            SELECT report_id, report_type, report_category, title, created_at
            FROM reports 
            ORDER BY created_at DESC
        ''')
        
        all_reports = cursor.fetchall()
        
        if all_reports:
            print(f'✅ Retrieved {len(all_reports)} reports from database')
            
            # Group by category
            categories = {}
            for report in all_reports:
                category = report[2]  # report_category
                if category not in categories:
                    categories[category] = []
                categories[category].append(report)
            
            print(f'\n📊 REPORTS BY CATEGORY:')
            for category, reports in categories.items():
                print(f'   {category}: {len(reports)} reports')
                for report in reports[:3]:  # Show first 3
                    print(f'      - {report[0]}: {report[3]}')  # report_id, title
            
            # Test filtering by type
            cursor.execute('''
                SELECT COUNT(*) FROM reports 
                WHERE report_type = 'PAYROLL_AUDIT_REPORT'
            ''')
            
            audit_reports = cursor.fetchone()[0]
            print(f'\n📋 PAYROLL AUDIT REPORTS: {audit_reports}')
            
            return True
        else:
            print(f'❌ No reports found in database')
            return False
            
    except Exception as e:
        print(f'❌ Error retrieving reports: {e}')
        return False
    finally:
        conn.close()

def test_report_generation_workflow():
    """Test the complete report generation workflow"""
    print(f'\n🔄 TESTING COMPLETE REPORT GENERATION WORKFLOW')
    print("=" * 60)
    
    # Simulate the workflow that happens in the UI
    workflow_steps = [
        "1. User selects changes in Interactive Pre-Reporting UI",
        "2. Smart Report Generator processes data with business rules",
        "3. Report templates generate Word/PDF/Excel formats",
        "4. Report Manager saves generated reports to database",
        "5. User can access reports through Report Manager interface"
    ]
    
    print(f'📋 REPORT GENERATION WORKFLOW:')
    for step in workflow_steps:
        print(f'   {step}')
    
    # Test workflow components
    print(f'\n🧪 TESTING WORKFLOW COMPONENTS:')
    
    # Component 1: Check if UI files exist and are functional
    ui_components = [
        ('ui/interactive_pre_reporting.js', 'Interactive Pre-Reporting UI'),
        ('ui/smart_report_generator.js', 'Smart Report Generator'),
        ('ui/report_distinction_manager.js', 'Report Distinction Manager')
    ]
    
    ui_success = True
    for file_path, component_name in ui_components:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f'   ✅ {component_name}: {file_size:,} bytes')
            
            # Check for key functions
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            if 'generateReport' in content or 'saveToReportManager' in content:
                print(f'      ✅ Contains report generation functions')
            else:
                print(f'      ⚠️ May be missing key functions')
        else:
            print(f'   ❌ {component_name}: File not found')
            ui_success = False
    
    # Component 2: Test database connectivity
    print(f'\n   📊 DATABASE CONNECTIVITY:')
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Test reports table
        cursor.execute('SELECT COUNT(*) FROM reports')
        report_count = cursor.fetchone()[0]
        print(f'      ✅ Reports table accessible: {report_count} reports')
        
        # Test session data availability
        cursor.execute('SELECT COUNT(*) FROM audit_sessions')
        session_count = cursor.fetchone()[0]
        print(f'      ✅ Session data available: {session_count} sessions')
        
        # Test comparison results for report generation
        cursor.execute('SELECT COUNT(*) FROM comparison_results')
        comparison_count = cursor.fetchone()[0]
        print(f'      ✅ Comparison data available: {comparison_count} results')
        
        conn.close()
        db_success = True
    except Exception as e:
        print(f'      ❌ Database connectivity failed: {e}')
        db_success = False
    
    return ui_success and db_success

def fix_department_population_issue():
    """Fix the department population issue in tracker table"""
    print(f'\n🔧 FIXING DEPARTMENT POPULATION ISSUE')
    print("=" * 60)
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Get latest session
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    if not session_result:
        print('❌ No audit sessions found')
        return False
    
    session_id = session_result[0]
    print(f'📅 Using session: {session_id}')
    
    # The issue: tracker records have "LOOKUP_FAILED_COP" instead of actual departments
    # Solution: Use extracted_data PERSONAL DETAILS.DEPARTMENT as the reliable source
    
    def get_reliable_department(employee_id):
        """Get department from extracted_data PERSONAL DETAILS section"""
        cursor.execute('''
            SELECT item_value 
            FROM extracted_data 
            WHERE session_id = ? AND employee_id = ? 
            AND section_name = 'PERSONAL DETAILS' 
            AND item_label = 'DEPARTMENT'
            AND period_type = 'current'
            LIMIT 1
        ''', (session_id, employee_id))
        
        result = cursor.fetchone()
        if result and result[0] and result[0].strip():
            return result[0].strip()
        
        # Try previous period as fallback
        cursor.execute('''
            SELECT item_value 
            FROM extracted_data 
            WHERE session_id = ? AND employee_id = ? 
            AND section_name = 'PERSONAL DETAILS' 
            AND item_label = 'DEPARTMENT'
            AND period_type = 'previous'
            LIMIT 1
        ''', (session_id, employee_id))
        
        result = cursor.fetchone()
        if result and result[0] and result[0].strip():
            return result[0].strip()
        
        return 'DEPARTMENT_NOT_AVAILABLE'
    
    # Get tracker records with problematic departments
    cursor.execute('''
        SELECT DISTINCT employee_id, department
        FROM tracker_results 
        WHERE session_id = ? AND (
            department LIKE 'LOOKUP_FAILED_%' OR 
            department = 'Unknown' OR 
            department IS NULL
        )
    ''', (session_id,))
    
    problematic_records = cursor.fetchall()
    
    if problematic_records:
        print(f'Found {len(problematic_records)} employees with problematic departments:')
        
        fixed_count = 0
        for employee_id, old_dept in problematic_records:
            reliable_dept = get_reliable_department(employee_id)
            
            if reliable_dept != 'DEPARTMENT_NOT_AVAILABLE':
                # Update tracker_results
                cursor.execute('''
                    UPDATE tracker_results 
                    SET department = ?
                    WHERE session_id = ? AND employee_id = ?
                ''', (reliable_dept, session_id, employee_id))
                
                print(f'   {employee_id}: {old_dept} → {reliable_dept}')
                fixed_count += 1
            else:
                print(f'   {employee_id}: {old_dept} → NO DEPARTMENT DATA AVAILABLE')
        
        conn.commit()
        print(f'\n✅ Fixed {fixed_count} department records')
        
        # Also fix bank adviser tables
        bank_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table_name in bank_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if cursor.fetchone():
                cursor.execute(f'''
                    SELECT DISTINCT employee_no 
                    FROM {table_name} 
                    WHERE department LIKE 'LOOKUP_FAILED_%' OR department = 'Unknown' OR department IS NULL
                ''')
                
                bank_problematic = cursor.fetchall()
                
                if bank_problematic:
                    print(f'\n   🏦 Fixing {table_name}: {len(bank_problematic)} records')
                    
                    for (employee_no,) in bank_problematic:
                        reliable_dept = get_reliable_department(employee_no)
                        
                        if reliable_dept != 'DEPARTMENT_NOT_AVAILABLE':
                            cursor.execute(f'''
                                UPDATE {table_name} 
                                SET department = ?
                                WHERE employee_no = ?
                            ''', (reliable_dept, employee_no))
        
        conn.commit()
        conn.close()
        return True
    else:
        print(f'✅ No problematic department records found')
        conn.close()
        return True

if __name__ == "__main__":
    print("Testing Report Manager functionality and fixing department issues...")
    
    # Test 1: Report Manager save functionality
    save_success = test_report_manager_save()
    
    # Test 2: Report retrieval functionality
    retrieval_success = test_report_retrieval()
    
    # Test 3: Complete workflow testing
    workflow_success = test_report_generation_workflow()
    
    # Fix 4: Department population issue
    dept_fix_success = fix_department_population_issue()
    
    print(f'\n🎯 COMPREHENSIVE TEST RESULTS:')
    print(f'   Report Manager save: {"✅ WORKING" if save_success else "❌ FAILED"}')
    print(f'   Report retrieval: {"✅ WORKING" if retrieval_success else "❌ FAILED"}')
    print(f'   Workflow components: {"✅ READY" if workflow_success else "❌ MISSING"}')
    print(f'   Department fix: {"✅ APPLIED" if dept_fix_success else "❌ FAILED"}')
    
    if save_success and retrieval_success and workflow_success and dept_fix_success:
        print(f'\n🎉 ALL SYSTEMS FUNCTIONAL!')
        print(f'   ✅ Report Manager can save and retrieve reports')
        print(f'   ✅ Report generation workflow components are ready')
        print(f'   ✅ Department population issue has been fixed')
        print(f'   ✅ Ready for end-to-end testing')
    else:
        print(f'\n💡 ISSUES TO ADDRESS:')
        if not save_success:
            print(f'   - Report Manager save functionality needs debugging')
        if not retrieval_success:
            print(f'   - Report retrieval system needs attention')
        if not workflow_success:
            print(f'   - Report generation workflow components missing')
        if not dept_fix_success:
            print(f'   - Department population fix failed')
