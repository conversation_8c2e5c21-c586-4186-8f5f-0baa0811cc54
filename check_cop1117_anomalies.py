#!/usr/bin/env python3
"""
Check COP1117 Anomalies - Investigate the specific employee you mentioned
"""

import sqlite3

def check_cop1117():
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()

    session_id = 'audit_session_1751969578_07067c76'

    print('=== CHECK FOR EMPLOYEE COP1117 ===')
    cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND employee_id = ?', (session_id, 'COP1117'))
    cop1117_count = cursor.fetchone()[0]
    print(f'Records for COP1117: {cop1117_count}')

    if cop1117_count > 0:
        print('\n=== COP1117 LOANS SECTION DATA ===')
        cursor.execute('''
            SELECT section_name, item_label, item_value, period_type
            FROM extracted_data 
            WHERE session_id = ? AND employee_id = ? AND section_name = ?
            ORDER BY item_label, period_type
        ''', (session_id, 'COP1117', 'LOANS'))
        loans_data = cursor.fetchall()
        
        for section, item, value, period in loans_data:
            print(f'  {period}: {item} = {value}')
        
        print('\n=== CHECK FOR DUPLICATES IN COP1117 LOANS ===')
        cursor.execute('''
            SELECT item_label, COUNT(*) as count, GROUP_CONCAT(item_value, ' | ') as all_values
            FROM extracted_data 
            WHERE session_id = ? AND employee_id = ? AND section_name = ? AND period_type = ?
            GROUP BY item_label
            HAVING COUNT(*) > 1
        ''', (session_id, 'COP1117', 'LOANS', 'current'))
        duplicates = cursor.fetchall()
        
        if duplicates:
            print('🚨 FOUND DUPLICATES:')
            for item, count, values in duplicates:
                print(f'  {item}: appears {count} times with values: {values}')
        else:
            print('No duplicates found in COP1117 LOANS section')
            
        # Check if this is July 2025 data
        print('\n=== CHECK WHAT MONTH THIS DATA IS ===')
        cursor.execute('''
            SELECT DISTINCT created_at
            FROM extracted_data 
            WHERE session_id = ? AND employee_id = ?
            LIMIT 5
        ''', (session_id, 'COP1117'))
        dates = cursor.fetchall()
        for (date,) in dates:
            print(f'  Data created: {date}')
            
    else:
        print('❌ COP1117 not found in database')
        print('\n=== CHECK WHAT EMPLOYEES ARE IN DATABASE ===')
        cursor.execute('''
            SELECT DISTINCT employee_id
            FROM extracted_data 
            WHERE session_id = ?
            ORDER BY employee_id
            LIMIT 20
        ''', (session_id,))
        employees = cursor.fetchall()
        print('Sample employees in database:')
        for (emp_id,) in employees:
            print(f'  {emp_id}')

    conn.close()

if __name__ == "__main__":
    check_cop1117()
