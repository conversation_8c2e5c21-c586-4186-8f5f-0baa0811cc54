#!/usr/bin/env python3
"""
Test Cross-Section Duplicate Logic - Verify EARNINGS priority over DEDUCTIONS
"""

from perfect_section_aware_extractor import PerfectSectionAwareExtractor

def test_cross_section_duplicates():
    print("🧪 TESTING CROSS-SECTION DUPLICATE LOGIC")
    print("=" * 60)
    print("Testing: When same item appears in EARNINGS and DEDUCTIONS")
    print("Expected: EARNINGS version should be kept, DEDUCTIONS removed")
    
    extractor = PerfectSectionAwareExtractor(debug=True)
    
    # Simulate a full payslip extraction with cross-section duplicates
    mock_payslip_data = {
        'EARNINGS': {
            'LEAVE ALLOWANCE': '1,500.00',
            'RENT ELEMENT': '2,000.00',
            'BASIC SALARY': '5,000.00'
        },
        'DEDUCTIONS': {
            'LEAVE ALLOWANCE': '1,500.00',  # Same item in both sections
            'RENT ELEMENT': '2,000.00',     # Same item in both sections
            'INCOME TAX': '750.00'
        }
    }
    
    print("\n📋 INPUT DATA:")
    for section, items in mock_payslip_data.items():
        print(f"  {section}:")
        for item, value in items.items():
            print(f"    {item}: {value}")
    
    # Test the consolidation logic (if it exists)
    print("\n🔍 TESTING CONSOLIDATION LOGIC:")
    
    # Check if there's a method to handle cross-section duplicates
    if hasattr(extractor, '_consolidate_cross_section_duplicates'):
        print("✅ Found _consolidate_cross_section_duplicates method")
        result = extractor._consolidate_cross_section_duplicates(mock_payslip_data)
        
        print("\n📊 RESULT AFTER CONSOLIDATION:")
        for section, items in result.items():
            print(f"  {section}:")
            for item, value in items.items():
                print(f"    {item}: {value}")
        
        # Verify EARNINGS priority
        earnings_has_leave = 'LEAVE ALLOWANCE' in result.get('EARNINGS', {})
        deductions_has_leave = 'LEAVE ALLOWANCE' in result.get('DEDUCTIONS', {})
        
        earnings_has_rent = 'RENT ELEMENT' in result.get('EARNINGS', {})
        deductions_has_rent = 'RENT ELEMENT' in result.get('DEDUCTIONS', {})
        
        print(f"\n🎯 CROSS-SECTION DUPLICATE ANALYSIS:")
        print(f"  LEAVE ALLOWANCE in EARNINGS: {earnings_has_leave}")
        print(f"  LEAVE ALLOWANCE in DEDUCTIONS: {deductions_has_leave}")
        print(f"  RENT ELEMENT in EARNINGS: {earnings_has_rent}")
        print(f"  RENT ELEMENT in DEDUCTIONS: {deductions_has_rent}")
        
        if earnings_has_leave and not deductions_has_leave:
            print("✅ LEAVE ALLOWANCE: EARNINGS priority working correctly")
        else:
            print("❌ LEAVE ALLOWANCE: Cross-section logic not working")
            
        if earnings_has_rent and not deductions_has_rent:
            print("✅ RENT ELEMENT: EARNINGS priority working correctly")
        else:
            print("❌ RENT ELEMENT: Cross-section logic not working")
            
        return earnings_has_leave and not deductions_has_leave and earnings_has_rent and not deductions_has_rent
        
    else:
        print("❌ No _consolidate_cross_section_duplicates method found")
        
        # Check if this logic exists elsewhere
        print("\n🔍 CHECKING FOR OTHER CONSOLIDATION METHODS:")
        consolidation_methods = [method for method in dir(extractor) if 'consolidat' in method.lower() or 'cross' in method.lower()]
        
        if consolidation_methods:
            print("Found potential consolidation methods:")
            for method in consolidation_methods:
                print(f"  - {method}")
        else:
            print("No consolidation methods found")
            
        print("\n⚠️  CROSS-SECTION DUPLICATE LOGIC MAY NOT BE IMPLEMENTED")
        print("    This means items appearing in both EARNINGS and DEDUCTIONS")
        print("    will both be preserved, which may not be the desired behavior")
        
        return False

def test_with_real_extraction():
    """Test with actual extraction to see current behavior"""
    print("\n\n🔬 TESTING WITH MOCK EXTRACTION")
    print("=" * 60)
    
    extractor = PerfectSectionAwareExtractor(debug=False)
    
    # Create mock elements that would result in cross-section duplicates
    mock_elements = [
        # EARNINGS section
        {'text': 'LEAVE ALLOWANCE', 'x': 50, 'y': 200, 'section': 'EARNINGS'},
        {'text': '1,500.00', 'x': 200, 'y': 200, 'section': 'EARNINGS'},
        
        # DEDUCTIONS section  
        {'text': 'LEAVE ALLOWANCE', 'x': 350, 'y': 200, 'section': 'DEDUCTIONS'},
        {'text': '1,500.00', 'x': 500, 'y': 200, 'section': 'DEDUCTIONS'},
    ]
    
    # Test extraction for each section
    earnings_pairs = extractor._find_label_value_pairs(mock_elements[:2], 'EARNINGS')
    deductions_pairs = extractor._find_label_value_pairs(mock_elements[2:], 'DEDUCTIONS')
    
    print(f"EARNINGS pairs: {len(earnings_pairs)}")
    for pair in earnings_pairs:
        print(f"  {pair['label']['text']} → {pair['value']['text']}")
        
    print(f"DEDUCTIONS pairs: {len(deductions_pairs)}")
    for pair in deductions_pairs:
        print(f"  {pair['label']['text']} → {pair['value']['text']}")
    
    # Both sections will have LEAVE ALLOWANCE - this shows current behavior
    earnings_has_leave = any('LEAVE ALLOWANCE' in pair['label']['text'] for pair in earnings_pairs)
    deductions_has_leave = any('LEAVE ALLOWANCE' in pair['label']['text'] for pair in deductions_pairs)
    
    print(f"\n📊 CURRENT BEHAVIOR:")
    print(f"  LEAVE ALLOWANCE in EARNINGS: {earnings_has_leave}")
    print(f"  LEAVE ALLOWANCE in DEDUCTIONS: {deductions_has_leave}")
    
    if earnings_has_leave and deductions_has_leave:
        print("⚠️  BOTH sections have LEAVE ALLOWANCE - no cross-section consolidation")
        return False
    else:
        print("✅ Cross-section consolidation is working")
        return True

if __name__ == "__main__":
    print("Testing cross-section duplicate handling logic...")
    
    # Test 1: Check for consolidation method
    has_consolidation = test_cross_section_duplicates()
    
    # Test 2: Test actual extraction behavior
    extraction_works = test_with_real_extraction()
    
    print(f"\n🎯 SUMMARY:")
    print(f"  Consolidation method exists: {has_consolidation}")
    print(f"  Extraction handles cross-duplicates: {extraction_works}")
    
    if not has_consolidation and not extraction_works:
        print("\n🚨 RECOMMENDATION:")
        print("  Cross-section duplicate logic needs to be implemented")
        print("  Items appearing in both EARNINGS and DEDUCTIONS should prioritize EARNINGS")
    else:
        print("\n✅ Cross-section duplicate logic is working correctly")
