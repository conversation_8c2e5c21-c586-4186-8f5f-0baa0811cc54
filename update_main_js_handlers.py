#!/usr/bin/env python3
"""
Update Main.js Handlers for Report Generation
"""

import os

def update_main_js_handlers():
    """Update main.js to include the report generation bridge"""
    print("🔧 UPDATING MAIN.JS HANDLERS")
    print("=" * 50)
    
    main_js_path = 'main.js'
    
    try:
        with open(main_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if handler already exists
        if "generate-actual-report-files" in content:
            print("   ✅ Handler already exists in main.js")
            return True
        
        # Find insertion point after existing report handlers
        insertion_point = content.find("// Generate PDF document using PDF Template Engine")
        
        if insertion_point == -1:
            print("   ❌ Could not find insertion point in main.js")
            return False
        
        # New handler code
        new_handler = '''
// Generate actual report files using bridge script
ipcMain.handle('generate-actual-report-files', async (event, sessionId) => {
  try {
    console.log('[REPORT-FILES] Generating actual Word and PDF files for session:', sessionId);
    
    const pythonPath = path.join(__dirname, 'core', 'report_generation_bridge.py');
    const result = await runHybridScript(pythonPath, ['generate-report', sessionId || '']);
    
    console.log('[REPORT-FILES] File generation result:', result);
    
    const parsedResult = JSON.parse(result);
    
    if (parsedResult.success) {
      console.log('[REPORT-FILES] ✅ Report files generated successfully');
      console.log('[REPORT-FILES] Files:', parsedResult.files);
    } else {
      console.log('[REPORT-FILES] ❌ Report generation failed:', parsedResult.error);
    }
    
    return parsedResult;
  } catch (error) {
    console.error('[REPORT-FILES] File generation failed:', error);
    return { success: false, error: error.message };
  }
});

'''
        
        # Insert the new handler
        content = content[:insertion_point] + new_handler + content[insertion_point:]
        
        # Write back to file
        with open(main_js_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ Added generate-actual-report-files handler to main.js")
        return True
        
    except Exception as e:
        print(f"   ❌ Error updating main.js: {e}")
        return False

def update_preload_js():
    """Update preload.js to expose the new handler"""
    print(f"\n🔧 UPDATING PRELOAD.JS")
    print("=" * 50)
    
    preload_js_path = 'preload.js'
    
    try:
        with open(preload_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if API method already exists
        if "generateActualReportFiles" in content:
            print("   ✅ API method already exists in preload.js")
            return True
        
        # Find the report generation section
        insertion_point = content.find("generateReport: (data, options) => ipcRenderer.invoke('generate-report', data, options),")
        
        if insertion_point == -1:
            print("   ❌ Could not find insertion point in preload.js")
            return False
        
        # Find the end of the line
        end_point = content.find(",", insertion_point) + 1
        
        # New API method
        new_method = "\n  generateActualReportFiles: (sessionId) => ipcRenderer.invoke('generate-actual-report-files', sessionId),"
        
        # Insert the new method
        content = content[:end_point] + new_method + content[end_point:]
        
        # Write back to file
        with open(preload_js_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ Added generateActualReportFiles to preload.js")
        return True
        
    except Exception as e:
        print(f"   ❌ Error updating preload.js: {e}")
        return False

def update_renderer_js():
    """Update renderer.js to call the new report generation method"""
    print(f"\n🔧 UPDATING RENDERER.JS")
    print("=" * 50)
    
    renderer_js_path = 'renderer.js'
    
    try:
        with open(renderer_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the generateReportsWithProgress function
        function_start = content.find("async function generateReportsWithProgress(reportData, options)")
        
        if function_start == -1:
            print("   ❌ Could not find generateReportsWithProgress function")
            return False
        
        # Find the actual backend API call
        api_call_start = content.find("const result = await window.api.generateReport(reportData, options);", function_start)
        
        if api_call_start == -1:
            print("   ❌ Could not find API call in generateReportsWithProgress")
            return False
        
        # Replace the API call with our new method
        old_call = "const result = await window.api.generateReport(reportData, options);"
        new_call = '''// Generate actual report files
      const result = await window.api.generateActualReportFiles(reportData.session_id || null);
      
      // Also call the original method for compatibility
      if (result.success) {
        try {
          await window.api.generateReport(reportData, options);
        } catch (error) {
          console.warn('Original generateReport failed, but files were created:', error);
        }
      }'''
        
        content = content.replace(old_call, new_call)
        
        # Write back to file
        with open(renderer_js_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ Updated generateReportsWithProgress to use actual file generation")
        return True
        
    except Exception as e:
        print(f"   ❌ Error updating renderer.js: {e}")
        return False

def create_test_session_data():
    """Create test session data for testing"""
    print(f"\n🧪 CREATING TEST SESSION DATA")
    print("=" * 50)
    
    try:
        import sqlite3
        from datetime import datetime
        
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Create test session
        test_session_id = f'test_session_{int(datetime.now().timestamp())}'
        
        cursor.execute('''
            INSERT INTO audit_sessions (session_id, created_at)
            VALUES (?, ?)
        ''', (test_session_id, datetime.now().isoformat()))
        
        # Create test comparison results
        test_data = [
            ('COP001', 'BASIC SALARY', 'INCREASED', '5000.00', '4500.00'),
            ('COP001', 'LEAVE ALLOWANCE', 'NEW', '500.00', None),
            ('COP002', 'BASIC SALARY', 'DECREASED', '4000.00', '4200.00'),
            ('COP002', 'INCOME TAX', 'INCREASED', '800.00', '750.00'),
        ]
        
        for emp_id, item_label, change_type, current_value, previous_value in test_data:
            cursor.execute('''
                INSERT INTO comparison_results 
                (session_id, employee_id, item_label, change_type, current_value, previous_value)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (test_session_id, emp_id, item_label, change_type, current_value, previous_value))
        
        conn.commit()
        conn.close()
        
        print(f"   ✅ Test session created: {test_session_id}")
        print(f"   ✅ Test comparison data added: {len(test_data)} records")
        
        # Test the bridge script with this data
        import subprocess
        import sys
        import json
        
        result = subprocess.run([
            sys.executable, 
            'core/report_generation_bridge.py', 
            'generate-report', 
            test_session_id
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            try:
                output = json.loads(result.stdout)
                if output.get('success'):
                    print(f"   ✅ Bridge script test successful")
                    print(f"      Report ID: {output.get('report_id')}")
                    
                    # Check if files exist
                    files = output.get('files', {})
                    for file_type, file_path in files.items():
                        if os.path.exists(file_path):
                            file_size = os.path.getsize(file_path)
                            print(f"      ✅ {file_type}: {file_path} ({file_size:,} bytes)")
                        else:
                            print(f"      ❌ {file_type}: {file_path} (NOT FOUND)")
                    
                    return True
                else:
                    print(f"   ❌ Bridge script failed: {output.get('error')}")
                    return False
            except json.JSONDecodeError:
                print(f"   ❌ Invalid JSON output from bridge script")
                return False
        else:
            print(f"   ❌ Bridge script execution failed: {result.stderr}")
            return False
        
    except Exception as e:
        print(f"   ❌ Error creating test data: {e}")
        return False

if __name__ == "__main__":
    print("Updating handlers for report generation...")
    
    # Step 1: Update main.js
    main_js_success = update_main_js_handlers()
    
    # Step 2: Update preload.js
    preload_success = update_preload_js()
    
    # Step 3: Update renderer.js
    renderer_success = update_renderer_js()
    
    # Step 4: Create test data and test
    test_success = create_test_session_data()
    
    print(f'\n🎯 UPDATE RESULTS:')
    print(f'   Main.js update: {"✅ SUCCESS" if main_js_success else "❌ FAILED"}')
    print(f'   Preload.js update: {"✅ SUCCESS" if preload_success else "❌ FAILED"}')
    print(f'   Renderer.js update: {"✅ SUCCESS" if renderer_success else "❌ FAILED"}')
    print(f'   Test with data: {"✅ WORKING" if test_success else "❌ FAILED"}')
    
    if main_js_success and preload_success and renderer_success and test_success:
        print(f'\n🎉 REPORT GENERATION FIX COMPLETE!')
        print(f'   ✅ Bridge script creates actual files')
        print(f'   ✅ Main.js handlers updated')
        print(f'   ✅ Preload.js API methods added')
        print(f'   ✅ Renderer.js calls new methods')
        print(f'   ✅ Test data confirms functionality')
        print(f'\n🚀 NEXT STEPS:')
        print(f'   1. Restart the Electron application')
        print(f'   2. Run a payroll audit to generate session data')
        print(f'   3. Generate reports through the UI')
        print(f'   4. Verify files are created and downloads work')
    else:
        print(f'\n⚠️ SOME UPDATES FAILED')
        print(f'   Check the error messages above for details')
        print(f'   Manual fixes may be required')
