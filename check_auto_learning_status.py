#!/usr/bin/env python3
"""
Check Auto-Learning Status - Diagnostic Script
Analyzes why Auto-Learning might not be finding new items
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    return None

def check_auto_learning_status():
    """Check Auto-Learning status and diagnose issues"""
    print("🔍 AUTO-LEARNING STATUS CHECK")
    print("=" * 50)
    
    db_path = get_database_path()
    if not db_path:
        print("❌ Database not found")
        return
    
    print(f"✅ Database found: {db_path}")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Get latest session
            cursor.execute('SELECT session_id, status, created_at FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
            session = cursor.fetchone()
            
            if not session:
                print("❌ No sessions found")
                return
            
            session_id = session[0]
            print(f"📋 Latest session: {session_id}")
            print(f"📋 Status: {session[1]}")
            
            # Check if auto_learning_results table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auto_learning_results'")
            table_exists = cursor.fetchone()
            
            if not table_exists:
                print("❌ auto_learning_results table does not exist")
                return
            
            print("✅ auto_learning_results table exists")
            
            # Check auto-learning results for this session
            cursor.execute('SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?', (session_id,))
            session_auto_count = cursor.fetchone()[0]
            print(f"📊 Auto-learning results for this session: {session_auto_count}")
            
            # Check total auto-learning results
            cursor.execute('SELECT COUNT(*) FROM auto_learning_results')
            total_auto_count = cursor.fetchone()[0]
            print(f"📊 Total auto-learning results in database: {total_auto_count}")
            
            if total_auto_count > 0:
                # Show sessions with auto-learning data
                cursor.execute('''
                    SELECT session_id, COUNT(*) as count, 
                           SUM(CASE WHEN auto_approved = 1 THEN 1 ELSE 0 END) as approved,
                           SUM(CASE WHEN auto_approved = 0 AND dictionary_updated = 0 THEN 1 ELSE 0 END) as pending
                    FROM auto_learning_results 
                    GROUP BY session_id 
                    ORDER BY COUNT(*) DESC 
                    LIMIT 5
                ''')
                sessions_data = cursor.fetchall()
                
                print("\n📈 Sessions with auto-learning data:")
                for sess_id, count, approved, pending in sessions_data:
                    print(f"  {sess_id}: {count} total ({approved} approved, {pending} pending)")
            
            # Check extracted items for this session
            cursor.execute('SELECT COUNT(*) FROM extracted_items WHERE session_id = ?', (session_id,))
            extracted_count = cursor.fetchone()[0]
            print(f"📊 Extracted items for this session: {extracted_count}")
            
            # Check dictionary items
            cursor.execute('SELECT COUNT(*) FROM dictionary_items')
            dict_count = cursor.fetchone()[0]
            print(f"📊 Dictionary items: {dict_count}")
            
            if extracted_count > 0 and dict_count > 0:
                # Sample some extracted items to see what's being extracted
                cursor.execute('''
                    SELECT DISTINCT section_name, item_label 
                    FROM extracted_items 
                    WHERE session_id = ? 
                    ORDER BY section_name, item_label 
                    LIMIT 10
                ''', (session_id,))
                sample_extracted = cursor.fetchall()
                
                print("\n📋 Sample extracted items:")
                for section, item in sample_extracted:
                    print(f"  {section}: {item}")
                
                # Check if these items are in dictionary
                print("\n🔍 Checking if sample items are in dictionary:")
                for section, item in sample_extracted[:5]:  # Check first 5
                    cursor.execute('''
                        SELECT COUNT(*) FROM dictionary_items 
                        WHERE item_name = ? AND section_name = ?
                    ''', (item, section))
                    in_dict = cursor.fetchone()[0] > 0
                    status = "✅ In dictionary" if in_dict else "❌ NOT in dictionary"
                    print(f"  {section}: {item} - {status}")
            
            # Check comparison results
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
            comparison_count = cursor.fetchone()[0]
            print(f"📊 Comparison results: {comparison_count}")
            
            print("\n🎯 DIAGNOSIS:")
            if session_auto_count == 0 and extracted_count > 0:
                print("❌ Auto-Learning phase may not have run or found no new items")
                print("   Possible reasons:")
                print("   1. All extracted items are already in the dictionary")
                print("   2. Auto-Learning phase failed silently")
                print("   3. Auto-Learning phase was skipped")
            elif session_auto_count > 0:
                print("✅ Auto-Learning phase ran and found new items")
            else:
                print("⚠️ No extracted data to analyze")
                
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_auto_learning_status()
