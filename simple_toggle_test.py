#!/usr/bin/env python3
"""
Simple Toggle Test - Quick verification of toggle functionality
"""

import sqlite3
import json
import os

def simple_toggle_test():
    print("🔍 SIMPLE TOGGLE FUNCTIONALITY TEST")
    print("=" * 50)
    
    # 1. Check dictionary file
    dict_path = 'core/payroll_dictionary.json'
    print(f'📋 Checking dictionary: {dict_path}')
    
    if os.path.exists(dict_path):
        print(f'✅ Dictionary file found')
        
        try:
            with open(dict_path, 'r') as f:
                dictionary = json.load(f)
            
            # Count sections and items
            total_sections = len(dictionary)
            total_items = 0
            excluded_items = 0
            
            for section_name, section_data in dictionary.items():
                if isinstance(section_data, dict) and 'items' in section_data:
                    items = section_data['items']
                    total_items += len(items)
                    
                    # Count excluded items
                    for item_name, item_data in items.items():
                        if isinstance(item_data, dict):
                            if (not item_data.get('include_new', True) or 
                                not item_data.get('include_increase', True) or 
                                not item_data.get('include_decrease', True)):
                                excluded_items += 1
            
            print(f'   Sections: {total_sections}')
            print(f'   Total items: {total_items}')
            print(f'   Items with exclusions: {excluded_items}')
            
        except Exception as e:
            print(f'❌ Error reading dictionary: {e}')
            return False
    else:
        print(f'❌ Dictionary file not found')
        return False
    
    # 2. Check database
    db_path = 'data/templar_payroll_auditor.db'
    print(f'\n🗄️ Checking database: {db_path}')
    
    if os.path.exists(db_path):
        print(f'✅ Database file found')
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get latest session
            cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
            session_result = cursor.fetchone()
            
            if session_result:
                session_id = session_result[0]
                print(f'   Latest session: {session_id}')
                
                # Count comparison results
                cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
                total_comparisons = cursor.fetchone()[0]
                print(f'   Total comparison results: {total_comparisons}')
                
                # Count by change type
                cursor.execute('''
                    SELECT change_type, COUNT(*) 
                    FROM comparison_results 
                    WHERE session_id = ? 
                    GROUP BY change_type
                ''', (session_id,))
                
                change_types = cursor.fetchall()
                print(f'   Change types:')
                for change_type, count in change_types:
                    print(f'     {change_type}: {count}')
                
            else:
                print(f'❌ No audit sessions found')
                return False
            
            conn.close()
            
        except Exception as e:
            print(f'❌ Error reading database: {e}')
            return False
    else:
        print(f'❌ Database file not found')
        return False
    
    return True

def test_salary_advance_pent():
    print(f'\n🧪 TESTING SALARY ADVANCE PENT CLASSIFICATION')
    print("=" * 50)
    
    try:
        from core.dictionary_manager import PayrollDictionaryManager
        
        dict_manager = PayrollDictionaryManager(debug=False)
        
        test_loan = "SALARY ADVANCE PENT."
        classification = dict_manager.classify_loan_type(test_loan)
        
        print(f'Loan: "{test_loan}"')
        print(f'Classification: {classification}')
        
        if "IN-HOUSE" in classification:
            print(f'✅ Correctly classified as IN-HOUSE')
            return True
        else:
            print(f'❌ Incorrectly classified as EXTERNAL')
            
            # Check why it's not classified correctly
            dictionary = dict_manager.get_dictionary()
            loans_section = dictionary.get('LOANS', {})
            
            # Check fixed items
            items = loans_section.get('items', {})
            print(f'\nChecking fixed items in LOANS section:')
            
            found_match = False
            for item_name, item_data in items.items():
                if item_data.get('is_fixed', False) and not item_data.get('is_column_header', False):
                    if 'SALARY ADVANCE' in item_name.upper():
                        print(f'   Found: {item_name}')
                        found_match = True
            
            if not found_match:
                print(f'   ❌ No matching fixed items found')
                print(f'   This explains why it\'s classified as EXTERNAL')
            
            return False
            
    except Exception as e:
        print(f'❌ Error testing classification: {e}')
        return False

if __name__ == "__main__":
    print("Running simple toggle and dictionary tests...")
    
    # Test 1: Basic functionality
    basic_success = simple_toggle_test()
    
    # Test 2: Specific loan classification
    loan_success = test_salary_advance_pent()
    
    print(f'\n🎯 RESULTS:')
    print(f'   Basic functionality: {"✅ PASS" if basic_success else "❌ FAIL"}')
    print(f'   Loan classification: {"✅ PASS" if loan_success else "❌ FAIL"}')
    
    if not basic_success:
        print(f'\n💡 Basic functionality issues detected')
    
    if not loan_success:
        print(f'\n💡 SALARY ADVANCE PENT is not being classified as IN-HOUSE')
        print(f'   Need to add it to the dictionary fixed items')
