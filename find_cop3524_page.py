#!/usr/bin/env python3
"""
Find COP3524 Page - Search through PDF pages to find COP3524
"""

import fitz  # PyMuPDF

def find_cop3524_page():
    print("🔍 SEARCHING FOR COP3524 IN PDF")
    print("=" * 50)
    
    pdf_path = "Payslips/MN PAYSLIPS JUL 2025.pdf"
    
    try:
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        print(f"📄 Total pages in PDF: {total_pages}")
        
        # Search for COP3524 in text
        found_pages = []
        
        # Search in chunks to avoid memory issues
        chunk_size = 100
        for start_page in range(0, total_pages, chunk_size):
            end_page = min(start_page + chunk_size, total_pages)
            print(f"🔍 Searching pages {start_page + 1} to {end_page}...")
            
            for page_num in range(start_page, end_page):
                page = doc[page_num]
                text = page.get_text()
                
                # Look for COP3524 in the text
                if 'COP3524' in text:
                    found_pages.append(page_num + 1)  # Convert to 1-based
                    print(f"✅ FOUND COP3524 on page {page_num + 1}")
                    
                    # Extract some context around COP3524
                    lines = text.split('\n')
                    for i, line in enumerate(lines):
                        if 'COP3524' in line:
                            print(f"   Context: {line.strip()}")
                            # Show a few lines around it
                            for j in range(max(0, i-2), min(len(lines), i+3)):
                                if j != i:
                                    print(f"     {lines[j].strip()}")
                            break
                    break
        
        doc.close()
        
        if found_pages:
            print(f"\n🎯 COP3524 found on pages: {found_pages}")
            return found_pages[0]  # Return first occurrence
        else:
            print("\n❌ COP3524 not found in PDF text")
            return None
            
    except Exception as e:
        print(f"❌ Error searching PDF: {e}")
        return None

if __name__ == "__main__":
    page_num = find_cop3524_page()
    if page_num:
        print(f"\n🎯 COP3524 is on page {page_num}")
        print("Now we can test the educational subsidy fix on this specific page!")
    else:
        print("\n❌ Could not locate COP3524 in the PDF")
        print("The employee might be in a different PDF or the ID format might be different")
