#!/usr/bin/env python3
"""
Test Anomaly Detection Fix - Test the corrected anomaly detection
"""

from core.enhanced_duplicate_checker import scan_session_anomalies

def test_anomaly_detection():
    print("🧪 TESTING ANOMALY DETECTION FIX")
    print("=" * 50)
    
    session_id = 'audit_session_1751969578_07067c76'
    db_path = 'data/templar_payroll_auditor.db'
    
    print(f"Testing session: {session_id}")
    print(f"Database: {db_path}")
    
    try:
        result = scan_session_anomalies(session_id, db_path)
        
        print(f"\n✅ Anomaly detection completed!")
        print(f"Success: {result['success']}")
        print(f"Anomalies detected: {result['anomalies_detected']}")
        print(f"Total duplicates found: {result.get('total_duplicates_found', 0)}")
        
        if result['anomalies_detected'] > 0:
            print(f"\n🚨 DETECTED ANOMALIES:")
            for i, anomaly in enumerate(result['anomalies'][:10]):  # Show first 10
                print(f"  {i+1}. Employee {anomaly['employee_id']}")
                print(f"     Section: {anomaly['section_name']}")
                print(f"     Item: {anomaly['item_label']}")
                print(f"     Occurrences: {anomaly['occurrence_count']}")
                print(f"     Severity: {anomaly['severity']}")
                print(f"     Permissive: {anomaly['is_permissive']}")
                print(f"     Description: {anomaly['description']}")
                print()
        else:
            print("\n✅ No anomalies detected (all duplicates are permissive)")
            
        # Check if page 463 has any anomalies
        page_463_anomalies = [a for a in result['anomalies'] if '463' in a['employee_id']]
        if page_463_anomalies:
            print(f"\n📋 PAGE 463 ANOMALIES:")
            for anomaly in page_463_anomalies:
                print(f"  Employee {anomaly['employee_id']}: {anomaly['section_name']} - {anomaly['item_label']} ({anomaly['occurrence_count']}x)")
        else:
            print(f"\n📋 No anomalies found for page 463")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_anomaly_detection()
