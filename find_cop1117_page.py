#!/usr/bin/env python3
"""
Find COP1117 Page - Search through PDF pages to find COP1117
"""

import fitz  # PyMuPDF
import re

def find_cop1117_page():
    print("🔍 SEARCHING FOR COP1117 IN PDF")
    print("=" * 50)
    
    pdf_path = "Payslips/MN PAYSLIPS JUL 2025.pdf"
    
    try:
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        print(f"📄 Total pages in PDF: {total_pages}")
        
        # Search for COP1117 in text
        found_pages = []
        
        # Search in chunks to avoid memory issues
        chunk_size = 50
        for start_page in range(0, total_pages, chunk_size):
            end_page = min(start_page + chunk_size, total_pages)
            print(f"🔍 Searching pages {start_page + 1} to {end_page}...")
            
            for page_num in range(start_page, end_page):
                page = doc[page_num]
                text = page.get_text()
                
                # Look for COP1117 in the text
                if 'COP1117' in text:
                    found_pages.append(page_num + 1)  # Convert to 1-based
                    print(f"✅ FOUND COP1117 on page {page_num + 1}")
                    
                    # Extract some context around COP1117
                    lines = text.split('\n')
                    for i, line in enumerate(lines):
                        if 'COP1117' in line:
                            print(f"   Context: {line.strip()}")
                            # Show a few lines around it
                            for j in range(max(0, i-2), min(len(lines), i+3)):
                                if j != i:
                                    print(f"     {lines[j].strip()}")
                            break
                    break
        
        doc.close()
        
        if found_pages:
            print(f"\n🎯 COP1117 found on pages: {found_pages}")
            return found_pages[0]  # Return first occurrence
        else:
            print("\n❌ COP1117 not found in PDF text")
            
            # Try alternative search patterns
            print("\n🔍 Trying alternative search patterns...")
            doc = fitz.open(pdf_path)
            
            # Search for patterns like "1117" near "Employee"
            for page_num in range(min(100, total_pages)):  # Search first 100 pages
                page = doc[page_num]
                text = page.get_text()
                
                # Look for 1117 near Employee
                if '1117' in text and ('Employee' in text or 'COP' in text):
                    print(f"✅ Found potential match on page {page_num + 1}")
                    lines = text.split('\n')
                    for line in lines:
                        if '1117' in line:
                            print(f"   Line: {line.strip()}")
                    return page_num + 1
            
            doc.close()
            return None
            
    except Exception as e:
        print(f"❌ Error searching PDF: {e}")
        return None

if __name__ == "__main__":
    page_num = find_cop1117_page()
    if page_num:
        print(f"\n🎯 COP1117 is on page {page_num}")
        print("Now we can test the duplicate detection on this specific page!")
    else:
        print("\n❌ Could not locate COP1117 in the PDF")
        print("The employee might be in a different PDF or the ID format might be different")
