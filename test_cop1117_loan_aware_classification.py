#!/usr/bin/env python3
"""
Test COP1117 Loan-Aware Classification - Verify NEW_LOAN classification instead of anomaly
"""

import sqlite3
from core.enhanced_duplicate_checker import EnhancedD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_cop1117_loan_classification():
    print("🧪 TESTING COP1117 LOAN-AWARE CLASSIFICATION")
    print("=" * 60)
    
    # Create test data that simulates COP1117's duplicate RENT ADVANCE
    test_data = {
        'session_id': 'test_session_cop1117',
        'employee_id': 'COP1117',
        'section_name': 'LOANS',
        'item_label': 'RENT ADVANCE',
        'occurrence_count': 2,
        'all_values': [
            "{'balance_bf': '23,000.00', 'current_deduction': '1,000.00', 'outstanding_balance': '22,000.00'}",
            "{'balance_bf': '24,000.00', 'current_deduction': '1,000.00', 'outstanding_balance': '23,000.00'}"
        ],
        'all_ids': ['1', '2']
    }
    
    print("📋 TEST DATA:")
    print(f"  Employee: {test_data['employee_id']}")
    print(f"  Item: {test_data['item_label']}")
    print(f"  Occurrences: {test_data['occurrence_count']}")
    print(f"  Values:")
    for i, value in enumerate(test_data['all_values']):
        print(f"    {i+1}. {value}")
    
    # Create duplicate checker instance (using DatabaseAnomalyScanner for the methods we need)
    from core.enhanced_duplicate_checker import DatabaseAnomalyScanner
    checker = DatabaseAnomalyScanner(debug=True)
    
    # Test the loan duplication analysis
    print(f"\n🔍 TESTING LOAN DUPLICATION ANALYSIS:")
    
    # Test 1: Check if it's identified as legitimate loan duplication
    is_legitimate = checker._is_legitimate_loan_duplication(
        test_data['item_label'], 
        test_data['all_values']
    )
    
    print(f"  Is legitimate loan duplication: {is_legitimate}")
    
    if is_legitimate:
        print("  ✅ PASS: Identified as legitimate loan event")
        
        # Test 2: Check loan event classification
        print(f"\n🏦 TESTING LOAN EVENT CLASSIFICATION:")
        event_type = checker._classify_loan_event(
            test_data['item_label'],
            test_data['all_values']
        )
        
        print(f"  Event type: {event_type}")
        
        if event_type == 'NEW_LOAN':
            print("  ✅ PASS: Correctly classified as NEW_LOAN")
        else:
            print(f"  ❌ FAIL: Expected NEW_LOAN, got {event_type}")
        
        # Test 3: Check loan-aware analysis trigger
        print(f"\n🎯 TESTING LOAN-AWARE ANALYSIS TRIGGER:")
        loan_result = checker._trigger_loan_aware_analysis(
            test_data['session_id'],
            test_data['employee_id'],
            test_data['item_label'],
            test_data['all_values']
        )
        
        print(f"  Analysis result: {loan_result}")
        
        if loan_result.get('is_loan_event'):
            print("  ✅ PASS: Triggered loan-aware analysis")
            print(f"  Event type: {loan_result.get('event_type')}")
            print(f"  Base loan name: {loan_result.get('base_loan_name')}")
        else:
            print("  ❌ FAIL: Did not trigger loan-aware analysis")
            
    else:
        print("  ❌ FAIL: Not identified as legitimate loan event")
    
    # Test 4: Full anomaly analysis (should return None for loan events)
    print(f"\n🚨 TESTING FULL ANOMALY ANALYSIS:")
    
    # Create a mock row object
    class MockRow:
        def __init__(self, data):
            for key, value in data.items():
                setattr(self, key, value)
        
        def __getitem__(self, key):
            return getattr(self, key)
    
    mock_row = MockRow({
        **test_data,
        'all_values': '|'.join(test_data['all_values']),
        'all_ids': '|'.join(test_data['all_ids'])
    })
    
    anomaly_result = checker._analyze_duplicate_item(mock_row)
    
    if anomaly_result is None:
        print("  ✅ PASS: No anomaly flagged (correctly identified as loan event)")
    else:
        print(f"  ❌ FAIL: Anomaly flagged: {anomaly_result}")
    
    return anomaly_result is None and is_legitimate and event_type == 'NEW_LOAN'

def test_with_real_database():
    """Test with real COP1117 data from database"""
    print(f"\n\n🔬 TESTING WITH REAL COP1117 DATABASE DATA")
    print("=" * 60)
    
    try:
        # Try multiple database paths
        db_paths = [
            'data/templar_payroll_auditor.db',
            'templar_payroll_auditor.db',
            r'C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db'
        ]

        conn = None
        for db_path in db_paths:
            try:
                conn = sqlite3.connect(db_path)
                # Test the connection
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM audit_sessions')
                print(f"✅ Connected to database: {db_path}")
                break
            except Exception as e:
                if conn:
                    conn.close()
                continue

        if not conn:
            print("❌ Could not connect to any database")
            return False
        cursor = conn.cursor()
        
        # Get the latest session
        cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return False
        
        session_id = session_result[0]
        print(f"📅 Using session: {session_id}")
        
        # Look for COP1117 loan duplicates
        cursor.execute("""
            SELECT employee_id, section_name, item_label, COUNT(*) as count,
                   GROUP_CONCAT(item_value, '|') as all_values
            FROM extracted_data
            WHERE session_id = ? AND employee_id = 'COP1117' AND section_name = 'LOANS'
            GROUP BY employee_id, section_name, item_label
            HAVING COUNT(*) > 1
        """, (session_id,))
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"✅ Found {len(duplicates)} duplicate loan items for COP1117:")
            
            for emp_id, section, item, count, values in duplicates:
                print(f"  {item}: appears {count} times")
                print(f"    Values: {values}")
                
                # Test the loan-aware classification on real data
                from core.enhanced_duplicate_checker import DatabaseAnomalyScanner
                checker = DatabaseAnomalyScanner(debug=True)
                
                value_list = values.split('|')
                is_legitimate = checker._is_legitimate_loan_duplication(item, value_list)
                event_type = checker._classify_loan_event(item, value_list)
                
                print(f"    Legitimate loan event: {is_legitimate}")
                print(f"    Event type: {event_type}")
                
                if is_legitimate and event_type == 'NEW_LOAN':
                    print(f"    ✅ CORRECTLY CLASSIFIED AS NEW_LOAN")
                else:
                    print(f"    ❌ CLASSIFICATION ISSUE")
            
            return True
        else:
            print("❌ No duplicate loan items found for COP1117")
            print("   This might mean:")
            print("   1. COP1117 data hasn't been extracted yet")
            print("   2. Duplicates were already processed")
            print("   3. The extraction didn't preserve duplicates")
            return False
            
    except Exception as e:
        print(f"❌ Error testing with real database: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("Testing COP1117 loan-aware classification system...")
    
    # Test 1: Mock data test
    mock_success = test_cop1117_loan_classification()
    
    # Test 2: Real database test
    real_success = test_with_real_database()
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"  Mock data test: {'✅ PASS' if mock_success else '❌ FAIL'}")
    print(f"  Real database test: {'✅ PASS' if real_success else '❌ FAIL'}")
    
    if mock_success and real_success:
        print(f"\n🎉 SUCCESS: COP1117 loan-aware classification is working!")
        print(f"   Duplicate RENT ADVANCE entries will be classified as NEW_LOAN events")
        print(f"   instead of being flagged as anomalies.")
    elif mock_success:
        print(f"\n⚠️ PARTIAL SUCCESS: Logic works but needs real data testing")
    else:
        print(f"\n❌ FAILED: Loan-aware classification needs debugging")
