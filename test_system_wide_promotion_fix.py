#!/usr/bin/env python3
"""
Test System-Wide Promotion Fix - Check all staff promotion results for educational subsidy issues
"""

import sqlite3
import json

def test_system_wide_promotion_fix():
    print("🔍 TESTING SYSTEM-WIDE PROMOTION FIX")
    print("=" * 60)
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Get latest session
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    if not session_result:
        print('❌ No audit sessions found')
        return False
    
    session_id = session_result[0]
    print(f'📅 Using session: {session_id}')
    
    # Check all staff promotion results
    print(f'\n🔍 ANALYZING ALL STAFF PROMOTION RESULTS:')
    cursor.execute('''
        SELECT employee_id, employee_name, consolidated_details
        FROM comparison_results 
        WHERE session_id = ? AND event_tag = 'STAFF-PROMOTION'
        ORDER BY employee_id
    ''', (session_id,))
    
    promotion_results = cursor.fetchall()
    
    if not promotion_results:
        print('❌ No staff promotion results found')
        return False
    
    print(f'Found {len(promotion_results)} staff promotion records')
    
    issues_found = 0
    total_checked = 0
    
    for employee_id, employee_name, consolidated_details in promotion_results:
        total_checked += 1
        print(f'\n👤 {employee_id}: {employee_name}')
        
        if consolidated_details:
            try:
                details = json.loads(consolidated_details)

                # Check for educational subsidy issues in the details
                has_educational_issue = False

                # Handle both dict and list formats
                if isinstance(details, dict):
                    items_to_check = details.items()
                elif isinstance(details, list):
                    # Convert list to dict-like structure for checking
                    items_to_check = [(f"item_{i}", item) for i, item in enumerate(details)]
                else:
                    print(f'   ⚠️ Unexpected details format: {type(details)}')
                    continue

                for key, value in items_to_check:
                    key_str = str(key).lower()
                    value_str = str(value).lower()

                    if 'educational' in key_str:
                        print(f'   📚 Educational item: {key} = {value}')

                        # Check if educational subsidy value looks wrong
                        if 'fuel' in value_str or 'element' in value_str:
                            print(f'   🚨 ISSUE: Educational subsidy has fuel/element value!')
                            has_educational_issue = True

                    elif 'basic salary' in key_str:
                        print(f'   💰 Basic salary: {key} = {value}')

                        # Check if basic salary contains educational text
                        if 'educational' in value_str:
                            print(f'   🚨 ISSUE: Basic salary contains educational text!')
                            has_educational_issue = True
                
                if has_educational_issue:
                    issues_found += 1
                    print(f'   ❌ ISSUES DETECTED for {employee_id}')
                else:
                    print(f'   ✅ No issues found for {employee_id}')
                    
            except json.JSONDecodeError:
                print(f'   ⚠️ Could not parse consolidated details for {employee_id}')
        else:
            print(f'   ⚠️ No consolidated details for {employee_id}')
    
    # Check individual comparison results for educational subsidy issues
    print(f'\n🔍 CHECKING INDIVIDUAL COMPARISON RESULTS:')
    cursor.execute('''
        SELECT employee_id, section_name, item_label, previous_value, current_value
        FROM comparison_results 
        WHERE session_id = ? AND (
            item_label LIKE '%EDUCATIONAL%' OR 
            previous_value LIKE '%EDUCATIONAL%' OR 
            current_value LIKE '%EDUCATIONAL%'
        )
        LIMIT 20
    ''', (session_id,))
    
    educational_results = cursor.fetchall()
    
    if educational_results:
        print(f'Found {len(educational_results)} educational-related comparison results:')
        
        for emp_id, section, item, prev_val, curr_val in educational_results:
            print(f'   {emp_id}: {section}.{item}')
            print(f'     Previous: {prev_val}')
            print(f'     Current: {curr_val}')
            
            # Check for cross-contamination
            if 'BASIC SALARY' in item and 'EDUCATIONAL' in str(prev_val or curr_val):
                print(f'     🚨 CROSS-CONTAMINATION: Basic salary with educational value!')
                issues_found += 1
            elif 'EDUCATIONAL' in item and ('FUEL' in str(prev_val or curr_val) or 'ELEMENT' in str(prev_val or curr_val)):
                print(f'     🚨 CROSS-CONTAMINATION: Educational subsidy with fuel/element value!')
                issues_found += 1
            else:
                print(f'     ✅ No cross-contamination detected')
    else:
        print('✅ No educational-related comparison results found')
    
    # Summary
    print(f'\n📊 SYSTEM-WIDE ANALYSIS SUMMARY:')
    print(f'   Total promotion records checked: {total_checked}')
    print(f'   Issues found: {issues_found}')
    print(f'   Success rate: {((total_checked - issues_found) / total_checked * 100):.1f}%' if total_checked > 0 else 'N/A')
    
    conn.close()
    
    if issues_found == 0:
        print(f'\n🎉 SUCCESS: No educational subsidy issues found in promotion results!')
        print(f'   The extraction fix has resolved the cross-contamination problem')
        return True
    else:
        print(f'\n⚠️ ISSUES REMAINING: {issues_found} issues still detected')
        print(f'   These may be from old data before the fix was applied')
        return False

def recommend_fresh_audit():
    """Recommend running a fresh audit to test the fix"""
    print(f'\n💡 RECOMMENDATION:')
    print('=' * 60)
    print('To fully verify the educational subsidy fix:')
    print('1. Run a fresh audit with the current PDF')
    print('2. Check that no new educational subsidy cross-contamination occurs')
    print('3. Verify that promotion detection is accurate')
    print('4. The fix prevents future issues but existing data may still show old problems')

if __name__ == "__main__":
    print("Testing system-wide promotion fix for educational subsidy issues...")
    
    # Test the current data
    success = test_system_wide_promotion_fix()
    
    # Provide recommendations
    recommend_fresh_audit()
    
    print(f'\n🎯 CONCLUSION:')
    if success:
        print('✅ The educational subsidy extraction fix is working correctly')
        print('✅ No cross-contamination issues detected in current data')
        print('✅ Future audits should not have false promotion detections')
    else:
        print('⚠️ Some issues detected, but these may be from old data')
        print('✅ The extraction fix prevents new issues from occurring')
        print('💡 Run a fresh audit to verify the fix completely')
