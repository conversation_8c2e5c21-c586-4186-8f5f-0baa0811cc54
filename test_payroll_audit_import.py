#!/usr/bin/env python3
"""
Test Payroll Audit Import - Verify the import error is fixed
"""

import sys
import os
import subprocess

def test_payroll_audit_import():
    """Test if the payroll audit process can start without import errors"""
    print("🧪 TESTING PAYROLL AUDIT PROCESS IMPORT")
    print("=" * 60)
    
    try:
        # Test importing the phased process manager directly
        print("📋 Step 1: Testing direct import...")
        
        # Add paths like the actual process would
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        core_dir = os.path.join(current_dir, 'core')
        if core_dir not in sys.path:
            sys.path.insert(0, core_dir)
        
        # Import the manager
        from core.phased_process_manager import PhasedProcessManager
        print("   ✅ PhasedProcessManager imported successfully")
        
        # Test creating an instance (minimal initialization)
        print("📋 Step 2: Testing class instantiation...")
        
        # Create a minimal test instance
        test_options = {
            'pdf_path': 'test.pdf',
            'debug_mode': True,
            'session_id': 'test_session'
        }
        
        # This should not fail with import errors
        manager = PhasedProcessManager(
            pdf_path=test_options['pdf_path'],
            debug_mode=test_options['debug_mode']
        )
        
        print("   ✅ PhasedProcessManager instance created successfully")
        
        # Test that the enhanced duplicate checker functions are accessible
        print("📋 Step 3: Testing duplicate prevention access...")
        
        # These should be available in the manager's scope
        from enhanced_duplicate_checker import should_add_to_auto_learning, is_item_duplicate
        
        print("   ✅ Duplicate prevention functions accessible")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
        
    except Exception as e:
        print(f"   ❌ Other error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_javascript_integration():
    """Test if the JavaScript can call the Python process without import errors"""
    print(f"\n🧪 TESTING JAVASCRIPT INTEGRATION")
    print("=" * 60)
    
    # Create a minimal test script that mimics what JavaScript would call
    test_script = '''
import sys
import os

# Add paths (same as in phased_process_manager.py)
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

try:
    from core.phased_process_manager import PhasedProcessManager
    print("SUCCESS: Import successful")
except Exception as e:
    print(f"ERROR: {e}")
    sys.exit(1)
'''
    
    # Write test script
    test_script_path = 'temp_import_test.py'
    with open(test_script_path, 'w') as f:
        f.write(test_script)
    
    try:
        # Run the test script as a subprocess (like JavaScript would)
        result = subprocess.run([sys.executable, test_script_path], 
                              capture_output=True, text=True, timeout=10)
        
        print(f"📋 Subprocess test result:")
        print(f"   Return code: {result.returncode}")
        print(f"   Stdout: {result.stdout.strip()}")
        
        if result.stderr:
            print(f"   Stderr: {result.stderr.strip()}")
        
        success = result.returncode == 0 and "SUCCESS" in result.stdout
        
        if success:
            print("   ✅ JavaScript integration should work")
        else:
            print("   ❌ JavaScript integration may still have issues")
        
        return success
        
    except subprocess.TimeoutExpired:
        print("   ❌ Test timed out")
        return False
        
    except Exception as e:
        print(f"   ❌ Subprocess test failed: {e}")
        return False
        
    finally:
        # Clean up test script
        if os.path.exists(test_script_path):
            os.remove(test_script_path)

def show_fix_summary():
    """Show what was fixed"""
    print(f"\n📋 IMPORT FIX SUMMARY")
    print("=" * 60)
    
    print(f"🔧 CHANGES MADE:")
    print(f"   1. Moved path setup BEFORE import statement")
    print(f"   2. Added current_dir (core) to sys.path")
    print(f"   3. Ensured enhanced_duplicate_checker is findable")
    
    print(f"\n📁 PATH STRUCTURE:")
    print(f"   Main directory: C:\\THE PAYROLL AUDITOR")
    print(f"   Core directory: C:\\THE PAYROLL AUDITOR\\core")
    print(f"   Enhanced checker: C:\\THE PAYROLL AUDITOR\\core\\enhanced_duplicate_checker.py")
    
    print(f"\n✅ EXPECTED BEHAVIOR:")
    print(f"   - JavaScript calls Python subprocess")
    print(f"   - Python adds core directory to path")
    print(f"   - Enhanced duplicate checker imports successfully")
    print(f"   - Auto-learning duplicate prevention works")

if __name__ == "__main__":
    print("Testing payroll audit import fix...")
    
    # Test 1: Direct import
    direct_success = test_payroll_audit_import()
    
    # Test 2: JavaScript integration simulation
    js_success = test_javascript_integration()
    
    # Show summary
    show_fix_summary()
    
    print(f'\n🎯 FINAL TEST RESULTS:')
    print(f'   Direct import: {"✅ WORKING" if direct_success else "❌ BROKEN"}')
    print(f'   JavaScript integration: {"✅ WORKING" if js_success else "❌ BROKEN"}')
    
    if direct_success and js_success:
        print(f'\n🎉 IMPORT FIX COMPLETE!')
        print(f'   ✅ Payroll audit process should start without import errors')
        print(f'   ✅ Auto-learning duplicate prevention is ready')
        print(f'   ✅ JavaScript integration should work properly')
    else:
        print(f'\n⚠️ SOME ISSUES REMAIN:')
        if not direct_success:
            print(f'   - Direct import still failing')
        if not js_success:
            print(f'   - JavaScript integration may have issues')
        print(f'   - May need additional path adjustments')
