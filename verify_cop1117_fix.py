#!/usr/bin/env python3
"""
Verify COP1117 Fix - Test if duplicate RENT ADVANCE entries are now detected
"""

import sqlite3
from perfect_section_aware_extractor import PerfectSectionAwareExtractor
from core.perfecto_data_converter import PerfectoDataConverter

def test_cop1117_extraction():
    print("🧪 TESTING COP1117 DUPLICATE DETECTION FIX")
    print("=" * 60)
    
    # Test extraction on a single page (COP1117 should be around page 1117)
    pdf_path = r"C:\Users\<USER>\OneDrive - The Church of Pentecost\Documents\AME PORT\SALARIES LIBRARY\SALARIES 2025\JULY 2025\SALARY UPDATE 2\MN PAYSLIPS JUL 2025 TEST FINAL.pdf"
    
    # Try to find COP1117 - it might be on page 1117 or nearby
    test_pages = [1117, 1116, 1118, 1115, 1119]  # Check around page 1117
    
    extractor = PerfectSectionAwareExtractor(debug=True)
    converter = PerfectoDataConverter(debug=True)
    
    for page_num in test_pages:
        print(f"\n🔍 TESTING PAGE {page_num} for COP1117...")
        
        try:
            # Extract raw data
            raw_data = extractor.extract_raw_data(pdf_path, page_num)
            
            if 'error' in raw_data:
                print(f"❌ Error on page {page_num}: {raw_data['error']}")
                continue
            
            # Convert to system format (this applies cross-section consolidation)
            conversion_result = converter.convert_to_system_format(raw_data)
            
            if not conversion_result['success']:
                print(f"❌ Conversion failed on page {page_num}")
                continue
                
            organized_data = conversion_result['employee_data']
            
            # Check if this page has COP1117
            personal_details = organized_data.get('PERSONAL DETAILS', {})
            employee_id = None
            
            # Look for employee ID in various possible field names
            for field in ['EMPLOYEE NO.', 'Employee No.', 'EMPLOYEE NUMBER', 'EMP NO']:
                if field in personal_details:
                    employee_id = personal_details[field]
                    break
            
            if employee_id and 'COP1117' in employee_id:
                print(f"✅ FOUND COP1117 on page {page_num}!")
                print(f"   Employee ID: {employee_id}")
                
                # Check LOANS section for duplicates
                loans_section = organized_data.get('LOANS', {})
                print(f"\n📋 LOANS SECTION ({len(loans_section)} items):")
                
                rent_advance_items = []
                salary_advance_items = []
                
                for item, value in loans_section.items():
                    print(f"   {item}: {value}")
                    
                    if 'RENT ADVANCE' in item.upper():
                        rent_advance_items.append((item, value))
                    elif 'SALARY ADVANCE' in item.upper():
                        salary_advance_items.append((item, value))
                
                print(f"\n🚨 DUPLICATE ANALYSIS:")
                print(f"   RENT ADVANCE items: {len(rent_advance_items)}")
                for item, value in rent_advance_items:
                    print(f"     {item}: {value}")
                    
                print(f"   SALARY ADVANCE items: {len(salary_advance_items)}")
                for item, value in salary_advance_items:
                    print(f"     {item}: {value}")
                
                # Check if duplicates were preserved
                if len(rent_advance_items) >= 2:
                    print("✅ SUCCESS: RENT ADVANCE duplicates preserved!")
                    return True, page_num, organized_data
                elif len(rent_advance_items) == 1:
                    print("⚠️  Only 1 RENT ADVANCE found - duplicates may have been consolidated")
                    return False, page_num, organized_data
                else:
                    print("❌ No RENT ADVANCE items found")
                    return False, page_num, organized_data
                    
            else:
                print(f"   Employee ID: {employee_id} (not COP1117)")
                
        except Exception as e:
            print(f"❌ Exception on page {page_num}: {e}")
            continue
    
    print("❌ COP1117 not found on any tested pages")
    return False, None, None

def test_anomaly_detection_on_cop1117(page_num, organized_data):
    """Test if anomaly detection now works on the extracted COP1117 data"""
    print(f"\n🔍 TESTING ANOMALY DETECTION ON COP1117 (Page {page_num})")
    print("=" * 60)
    
    # Simulate storing the data and running anomaly detection
    # (In real workflow, this would be stored in database first)
    
    loans_section = organized_data.get('LOANS', {})
    
    # Count duplicates manually
    item_counts = {}
    for item in loans_section.keys():
        # Remove duplicate suffixes to get base item name
        base_item = item.replace('_DUPLICATE_2', '').replace('_DUPLICATE_3', '')
        item_counts[base_item] = item_counts.get(base_item, 0) + 1
    
    duplicates_found = {item: count for item, count in item_counts.items() if count > 1}
    
    print(f"📊 DUPLICATE ANALYSIS:")
    if duplicates_found:
        print("🚨 DUPLICATES DETECTED:")
        for item, count in duplicates_found.items():
            print(f"   {item}: appears {count} times")
        return True
    else:
        print("✅ No duplicates detected (all items appear once)")
        return False

if __name__ == "__main__":
    print("Testing COP1117 duplicate detection with our fixes...")
    
    # Test 1: Extract COP1117 data
    success, page_num, organized_data = test_cop1117_extraction()
    
    if success and organized_data:
        # Test 2: Check if anomaly detection would work
        anomaly_success = test_anomaly_detection_on_cop1117(page_num, organized_data)
        
        print(f"\n🎯 FINAL RESULTS:")
        print(f"   COP1117 found: ✅")
        print(f"   Duplicates preserved: ✅")
        print(f"   Anomaly detection ready: {'✅' if anomaly_success else '❌'}")
        
        if success and anomaly_success:
            print("\n🎉 SUCCESS: The fixes are working! COP1117 duplicates will now be detected!")
        else:
            print("\n⚠️  Partial success - some issues remain")
    else:
        print(f"\n❌ Could not verify COP1117 - employee not found or no duplicates preserved")
        print("   This could mean:")
        print("   1. COP1117 is on a different page number")
        print("   2. The extraction logic still needs refinement")
        print("   3. The PDF structure is different than expected")
