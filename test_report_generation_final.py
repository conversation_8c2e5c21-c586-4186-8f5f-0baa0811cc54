#!/usr/bin/env python3
"""
Final Test of Report Generation Fix
"""

import os
import sqlite3
import subprocess
import sys
import json

def test_with_existing_session():
    """Test report generation with existing session data"""
    print("🧪 TESTING WITH EXISTING SESSION DATA")
    print("=" * 50)
    
    try:
        # Get existing session
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
        result = cursor.fetchone()
        
        if result:
            session_id = result[0]
            print(f"   Using existing session: {session_id}")
            
            # Check comparison data
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
            count = cursor.fetchone()[0]
            print(f"   Comparison results available: {count}")
            
            if count > 0:
                # Test the bridge script
                result = subprocess.run([
                    sys.executable, 
                    'core/report_generation_bridge.py', 
                    'generate-report', 
                    session_id
                ], capture_output=True, text=True, timeout=30)
                
                print(f"   Bridge script return code: {result.returncode}")
                
                if result.returncode == 0:
                    try:
                        output = json.loads(result.stdout)
                        if output.get('success'):
                            print(f"   ✅ Report generation successful!")
                            print(f"      Report ID: {output.get('report_id')}")
                            
                            # Check files
                            files = output.get('files', {})
                            all_files_exist = True
                            
                            for file_type, file_path in files.items():
                                if os.path.exists(file_path):
                                    file_size = os.path.getsize(file_path)
                                    print(f"      ✅ {file_type}: {file_path} ({file_size:,} bytes)")
                                else:
                                    print(f"      ❌ {file_type}: {file_path} (NOT FOUND)")
                                    all_files_exist = False
                            
                            return all_files_exist
                        else:
                            print(f"   ❌ Report generation failed: {output.get('error')}")
                            return False
                    except json.JSONDecodeError:
                        print(f"   ❌ Invalid JSON output")
                        print(f"   Stdout: {result.stdout}")
                        return False
                else:
                    print(f"   ❌ Bridge script failed")
                    print(f"   Stderr: {result.stderr}")
                    return False
            else:
                print(f"   ❌ No comparison data available")
                return False
        else:
            print(f"   ❌ No existing sessions found")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Test error: {e}")
        return False

def verify_handler_updates():
    """Verify that the handler updates were successful"""
    print(f"\n🔍 VERIFYING HANDLER UPDATES")
    print("=" * 50)
    
    files_to_check = [
        ('main.js', 'generate-actual-report-files'),
        ('preload.js', 'generateActualReportFiles'),
        ('renderer.js', 'generateActualReportFiles')
    ]
    
    all_updated = True
    
    for file_path, search_term in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if search_term in content:
                print(f"   ✅ {file_path}: Contains {search_term}")
            else:
                print(f"   ❌ {file_path}: Missing {search_term}")
                all_updated = False
        else:
            print(f"   ❌ {file_path}: File not found")
            all_updated = False
    
    return all_updated

def check_reports_directory():
    """Check the reports directory structure"""
    print(f"\n📁 CHECKING REPORTS DIRECTORY")
    print("=" * 50)
    
    reports_dir = 'reports'
    
    if os.path.exists(reports_dir):
        print(f"   ✅ Reports directory exists")
        
        # List all files
        all_files = []
        for root, dirs, files in os.walk(reports_dir):
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                all_files.append((file_path, file_size))
        
        if all_files:
            print(f"   📄 Found {len(all_files)} files:")
            
            # Show recent files
            all_files.sort(key=lambda x: os.path.getmtime(x[0]), reverse=True)
            
            for file_path, file_size in all_files[:10]:  # Show 10 most recent
                rel_path = os.path.relpath(file_path, reports_dir)
                modified = os.path.getmtime(file_path)
                import datetime
                mod_time = datetime.datetime.fromtimestamp(modified).strftime('%Y-%m-%d %H:%M:%S')
                print(f"      - {rel_path} ({file_size:,} bytes, {mod_time})")
            
            return True
        else:
            print(f"   ⚠️ Directory exists but is empty")
            return False
    else:
        print(f"   ❌ Reports directory does not exist")
        return False

def show_final_summary():
    """Show final summary of the fix"""
    print(f"\n📋 REPORT GENERATION FIX SUMMARY")
    print("=" * 50)
    
    print(f"🔧 WHAT WAS FIXED:")
    print(f"   ❌ Before: Reports saved to database but no files created")
    print(f"   ❌ Before: Download failed with 'No downloadable files available'")
    print(f"   ❌ Before: UI showed reports but files didn't exist")
    
    print(f"\n✅ SOLUTION IMPLEMENTED:")
    print(f"   ✅ After: Created report_generation_bridge.py")
    print(f"   ✅ After: Bridge script generates actual Word/PDF files")
    print(f"   ✅ After: Updated main.js with generate-actual-report-files handler")
    print(f"   ✅ After: Updated preload.js with generateActualReportFiles API")
    print(f"   ✅ After: Updated renderer.js to call new file generation")
    
    print(f"\n🚀 EXPECTED BEHAVIOR:")
    print(f"   1. User generates report through UI")
    print(f"   2. System calls generateActualReportFiles")
    print(f"   3. Bridge script creates Word and PDF files")
    print(f"   4. Files are saved to reports directory")
    print(f"   5. Database updated with correct file paths")
    print(f"   6. Download functionality works with actual files")
    
    print(f"\n💡 NEXT STEPS:")
    print(f"   1. Restart the Electron application")
    print(f"   2. Run a payroll audit (if needed)")
    print(f"   3. Generate reports through Interactive Pre-Reporting")
    print(f"   4. Verify files are created in reports directory")
    print(f"   5. Test download functionality")

if __name__ == "__main__":
    print("Final test of report generation fix...")
    
    # Test 1: Verify handler updates
    handlers_ok = verify_handler_updates()
    
    # Test 2: Test with existing session
    generation_ok = test_with_existing_session()
    
    # Test 3: Check reports directory
    directory_ok = check_reports_directory()
    
    # Show summary
    show_final_summary()
    
    print(f'\n🎯 FINAL TEST RESULTS:')
    print(f'   Handler updates: {"✅ COMPLETE" if handlers_ok else "❌ INCOMPLETE"}')
    print(f'   Report generation: {"✅ WORKING" if generation_ok else "❌ FAILED"}')
    print(f'   Reports directory: {"✅ POPULATED" if directory_ok else "❌ EMPTY"}')
    
    if handlers_ok and generation_ok and directory_ok:
        print(f'\n🎉 REPORT GENERATION FIX SUCCESSFUL!')
        print(f'   ✅ All components updated and working')
        print(f'   ✅ Actual files are being generated')
        print(f'   ✅ Download functionality should now work')
        print(f'   ✅ Ready for production use')
    else:
        print(f'\n⚠️ SOME ISSUES REMAIN:')
        if not handlers_ok:
            print(f'   - Handler updates incomplete')
        if not generation_ok:
            print(f'   - Report generation not working')
        if not directory_ok:
            print(f'   - Reports directory issues')
        print(f'   - May need manual verification or fixes')
