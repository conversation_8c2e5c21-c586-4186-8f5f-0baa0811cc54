const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('api', {
  // File operations
  selectPdfFile: () => ipcRenderer.invoke('select-pdf-file'),

  // Payroll processing with Perfect Section-Aware Extractor
  processPayroll: (pdfPath) => ipcRenderer.invoke('process-payroll', pdfPath),
  extractPayrollData: (pdfPath, options) => ipcRenderer.invoke('extract-payroll-data', pdfPath, options),
  getExtractionResults: (pdfPath) => ipcRenderer.invoke('get-extraction-results', pdfPath),
  processLargePayroll: (pdfPath, options) => ipcRenderer.invoke('process-large-payroll', pdfPath, options),

  // Perfect Section-Aware Extractor Settings
  getExtractionSettings: () => ipcRenderer.invoke('get-extraction-settings'),
  updateExtractionSettings: (settings) => ipcRenderer.invoke('update-extraction-settings', settings),

  // Application Settings
  getAppSettings: () => ipcRenderer.invoke('get-app-settings'),
  saveAppSettings: (settings) => ipcRenderer.invoke('save-app-settings', settings),
  resetAppSettings: () => ipcRenderer.invoke('reset-app-settings'),

  // Report Manager
  getSavedReports: () => ipcRenderer.invoke('get-saved-reports'),
  saveToReportManager: (reportData) => ipcRenderer.invoke('save-to-report-manager', reportData),
  viewReport: (reportId) => ipcRenderer.invoke('view-report', reportId),
  downloadReportById: (reportId) => ipcRenderer.invoke('download-report-by-id', reportId),
  deleteReport: (reportId) => ipcRenderer.invoke('delete-report', reportId),
  exportAllReports: () => ipcRenderer.invoke('export-all-reports'),
  debugBankAdviserReports: () => ipcRenderer.invoke('debug-bank-adviser-reports'),
  migrateBankAdviserReports: () => ipcRenderer.invoke('migrate-bank-adviser-reports'),

  // Dictionary management
  getDictionary: () => ipcRenderer.invoke('get-dictionary'),
  getEnhancedDictionary: () => ipcRenderer.invoke('get-enhanced-dictionary'),
  getDictionaryStats: () => ipcRenderer.invoke('get-dictionary-stats'),
  saveDictionary: (dictionary) => ipcRenderer.invoke('save-dictionary', dictionary),
  saveEnhancedDictionary: (dictionary) => ipcRenderer.invoke('save-enhanced-dictionary', dictionary),
  resetDictionary: () => ipcRenderer.invoke('reset-dictionary'),
  resetEnhancedDictionary: () => ipcRenderer.invoke('reset-enhanced-dictionary'),
  importDictionary: () => ipcRenderer.invoke('import-dictionary'),
  exportDictionary: () => ipcRenderer.invoke('export-dictionary'),
  downloadDictionaryTemplate: () => ipcRenderer.invoke('download-dictionary-template'),

  // Enhanced Loan Management APIs
  createLoanType: (loanTypeName, classification) => ipcRenderer.invoke('create-loan-type', loanTypeName, classification),
  updateLoanTypeClassification: (loanTypeName, classification) => ipcRenderer.invoke('update-loan-type-classification', loanTypeName, classification),
  deleteLoanType: (loanTypeName) => ipcRenderer.invoke('delete-loan-type', loanTypeName),
  getLoanTypes: () => ipcRenderer.invoke('get-loan-types'),
  getLoanClassificationSummary: () => ipcRenderer.invoke('get-loan-classification-summary'),
  autoGroupLoanItems: () => ipcRenderer.invoke('auto-group-loan-items'),
  addItemToLoanType: (loanTypeName, itemName, columnType) => ipcRenderer.invoke('add-item-to-loan-type', loanTypeName, itemName, columnType),
  removeItemFromLoanType: (loanTypeName, itemName) => ipcRenderer.invoke('remove-item-from-loan-type', loanTypeName, itemName),
  detectUngroupedLoanItems: () => ipcRenderer.invoke('detect-ungrouped-loan-items'),
  exportLoanClassificationReport: (format) => ipcRenderer.invoke('export-loan-classification-report', format),

  // Report generation
  generateReport: (data,
  generateActualReportFiles: (sessionId) => ipcRenderer.invoke('generate-actual-report-files', sessionId), options) => ipcRenderer.invoke('generate-report', data, options),
  exportReport: (reportData, format) => ipcRenderer.invoke('export-report', reportData, format),
  downloadReport: (reportRequest) => ipcRenderer.invoke('download-report', reportRequest),
  beginAuditProcess: (currentPdf, previousPdf, options) => ipcRenderer.invoke('begin-audit-process', currentPdf, previousPdf, options),
  comparePayrolls: (currentPdf, previousPdf) => ipcRenderer.invoke('compare-payrolls', currentPdf, previousPdf),
  enhancedPayrollAudit: (currentPdf, previousPdf, options) => ipcRenderer.invoke('enhanced-payroll-audit', currentPdf, previousPdf, options),
  generateAuditReport: (data, options) => ipcRenderer.invoke('generate-audit-report', data, options),

  // Process control
  stopPayrollAudit: () => ipcRenderer.invoke('stop-payroll-audit'),

  // Auto-learning
  getAutoLearningData: () => ipcRenderer.invoke('get-auto-learning-data'),
  approveAutoLearningItem: (item) => ipcRenderer.invoke('approve-auto-learning-item', item),
  rejectAutoLearningItem: (item) => ipcRenderer.invoke('reject-auto-learning-item', item),

  // File operations
  openFile: (filePath) => ipcRenderer.invoke('open-file', filePath),

  // System operations
  getSystemStatus: () => ipcRenderer.invoke('get-system-status'),
  getProcessingProgress: () => ipcRenderer.invoke('get-processing-progress'),

  // Event listeners
  onProcessingUpdate: (callback) => {
    ipcRenderer.on('processing-update', callback);
    return () => ipcRenderer.removeListener('processing-update', callback);
  },

  onExtractionComplete: (callback) => {
    ipcRenderer.on('extraction-complete', callback);
    return () => ipcRenderer.removeListener('extraction-complete', callback);
  },

  onError: (callback) => {
    ipcRenderer.on('error', callback);
    return () => ipcRenderer.removeListener('error', callback);
  },

  // Real-time backend progress
  onBackendProgress: (callback) => {
    ipcRenderer.on('backend-progress', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('backend-progress', callback);
  },

  // Real-time extraction updates
  onRealTimeExtractionUpdate: (callback) => {
    ipcRenderer.on('realtime-extraction-update', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('realtime-extraction-update', callback);
  },
  
  // Enhanced progress updates from the new workflow
  onEnhancedProgressUpdate: (callback) => {
    ipcRenderer.on('enhanced-progress-update', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('enhanced-progress-update', callback);
  },

  // Phase completion event listeners
  onExtractionComplete: (callback) => {
    ipcRenderer.on('extraction-complete', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('extraction-complete', callback);
  },
  onPreAuditingComplete: (callback) => {
    ipcRenderer.on('pre-auditing-complete', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('pre-auditing-complete', callback);
  },
  onComparisonComplete: (callback) => {
    ipcRenderer.on('comparison-complete', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('comparison-complete', callback);
  },
  onTrackerFeedingComplete: (callback) => {
    ipcRenderer.on('tracker-feeding-complete', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('tracker-feeding-complete', callback);
  },
  onAutoLearningComplete: (callback) => {
    ipcRenderer.on('auto-learning-complete', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('auto-learning-complete', callback);
  },
  onReportGenerationComplete: (callback) => {
    ipcRenderer.on('report-generation-complete', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('report-generation-complete', callback);
  },

  onDatabaseProgress: (callback) => {
    ipcRenderer.on('database-progress', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('database-progress', callback);
  },

  onDatabasePerformance: (callback) => {
    ipcRenderer.on('database-performance', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('database-performance', callback);
  },

  onDatabaseError: (callback) => {
    ipcRenderer.on('database-error', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('database-error', callback);
  },

  // Migration event listeners
  onMigrationProgress: (callback) => {
    ipcRenderer.on('migration-progress', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('migration-progress', callback);
  },

  // Enhanced Auto-Learning API
  getPendingItems: () => ipcRenderer.invoke('get-pending-items'),
  approvePendingItem: (itemId, standardizedName, targetSection) => ipcRenderer.invoke('approve-pending-item', itemId, standardizedName, targetSection),
  rejectPendingItem: (itemId, reason) => ipcRenderer.invoke('reject-pending-item', itemId, reason),
  startAutoLearningSession: (sessionName) => ipcRenderer.invoke('start-auto-learning-session', sessionName),
  endAutoLearningSession: () => ipcRenderer.invoke('end-auto-learning-session'),
  getAutoLearningSessionSummary: () => ipcRenderer.invoke('get-auto-learning-session-summary'),
  approveAllPending: () => ipcRenderer.invoke('approve-all-pending'),
  rejectAllPending: () => ipcRenderer.invoke('reject-all-pending'),
  resetAutoLearning: () => ipcRenderer.invoke('reset-auto-learning'),
  cleanAllAppData: () => ipcRenderer.invoke('clean-all-app-data'),
  cleanExtractedData: (options) => ipcRenderer.invoke('clean-extracted-data', options),

  completePREReportingPhase: (selectedCount) => ipcRenderer.invoke('complete-pre-reporting-phase', selectedCount),
  updateItemSection: (itemId, newSection) => ipcRenderer.invoke('update-item-section', itemId, newSection),

  // PDF Sorter API
  getPdfInfo: (pdfPath) => ipcRenderer.invoke('get-pdf-info', pdfPath),
  sortPdf: (pdfPath, sortConfig) => ipcRenderer.invoke('sort-pdf', pdfPath, sortConfig),
  stopPdfSorting: () => ipcRenderer.invoke('stop-pdf-sorting'),
  saveTempFile: (arrayBuffer, fileName) => ipcRenderer.invoke('save-temp-file', arrayBuffer, fileName),

  // Data Builder API
  getDictionaryData: () => ipcRenderer.invoke('get-dictionary-data'),
  buildSpreadsheet: (buildConfig) => ipcRenderer.invoke('build-spreadsheet', buildConfig),
  cancelSpreadsheetBuild: () => ipcRenderer.invoke('cancel-spreadsheet-build'),
  downloadSpreadsheet: (outputPath) => ipcRenderer.invoke('download-spreadsheet', outputPath),
  openOutputFolder: (folderPath) => ipcRenderer.invoke('open-output-folder', folderPath),
  callDataBuilderAPI: (command, args) => ipcRenderer.invoke('call-data-builder-api', command, args),

  // Unified Database API
  getDatabaseStats: () => ipcRenderer.invoke('get-database-stats'),
  performDatabaseMaintenance: (daysOld) => ipcRenderer.invoke('perform-database-maintenance', daysOld),

  // Storage Migration API
  migrateStorageToDatabase: () => ipcRenderer.invoke('migrate-storage-to-database'),
  checkMigrationStatus: () => ipcRenderer.invoke('check-migration-status'),

  // ========================================
  // ENHANCED FEATURES API
  // ========================================

  // Enhanced payroll audit with phased execution
  enhancedPayrollAudit: (currentPdf, previousPdf, options) => ipcRenderer.invoke('enhanced-payroll-audit', currentPdf, previousPdf, options),

  // Load single source comparison data
  loadSingleSourceComparisonData: () => ipcRenderer.invoke('load-single-source-comparison-data'),
  
  // Get comparison data from database for pre-reporting UI
  getComparisonDataFromDatabase: (processId) => ipcRenderer.invoke('getComparisonDataFromDatabase', processId),

  // Interactive Reporting UI data operations (UPDATED: Direct comparison results)
  getComparisonResults: (sessionId) => ipcRenderer.invoke('get-comparison-results', sessionId),
  getLatestComparisonResults: () => ipcRenderer.invoke('get-latest-comparison-results'),

  // DEPRECATED: Pre-reporting methods (kept for backward compatibility)
  getPreReportingData: (sessionId) => ipcRenderer.invoke('get-comparison-results', sessionId),
  getLatestPreReportingData: () => ipcRenderer.invoke('get-latest-comparison-results'),
  getCurrentSessionId: () => ipcRenderer.invoke('get-current-session-id'),
  updatePreReportingSelections: (selections) => ipcRenderer.invoke('update-pre-reporting-selections', selections),

  // 🎯 BULLETPROOF: Tracker table population
  populateTrackerTables: () => ipcRenderer.invoke('populate-tracker-tables'),

  // Report generation
  generateFinalReports: (sessionId) => ipcRenderer.invoke('generate-final-reports', sessionId),
  generateWordTemplateDocument: (smartReportData) => ipcRenderer.invoke('generate-word-template-document', smartReportData),
  generatePDFTemplateDocument: (smartReportData) => ipcRenderer.invoke('generate-pdf-template-document', smartReportData),

  // Process control
  pauseProcess: () => ipcRenderer.invoke('pause-process'),
  resumeProcess: () => ipcRenderer.invoke('resume-process'),
  stopProcess: () => ipcRenderer.invoke('stop-process'),

  // PRODUCTION GUARD: Phase verification
  verifyPhaseData: (phaseName) => ipcRenderer.invoke('verify-phase-data', phaseName),

  // Tracker feeding operations
  feedTrackerTables: (currentData, previousData, month, year) => ipcRenderer.invoke('feed-tracker-tables', currentData, previousData, month, year),

  // Auto learning operations
  processAutoLearning: (currentData, month, year) => ipcRenderer.invoke('process-auto-learning', currentData, month, year),

  // Content switching initialization
  initializeContentSwitching: () => ipcRenderer.invoke('initialize-content-switching'),

  // Process control
  pauseEnhancedProcess: (processId) => ipcRenderer.invoke('pause-enhanced-process', processId),
  resumeEnhancedProcess: (processId) => ipcRenderer.invoke('resume-enhanced-process', processId),
  stopEnhancedProcess: (processId) => ipcRenderer.invoke('stop-enhanced-process', processId),

  // Clear enhanced listeners
  clearEnhancedListeners: () => {
    ipcRenderer.removeAllListeners('enhanced-progress-update');
    ipcRenderer.removeAllListeners('enhanced-log-update');
    ipcRenderer.removeAllListeners('enhanced-phase-update');
    ipcRenderer.removeAllListeners('enhanced-tracker-update');
    ipcRenderer.removeAllListeners('enhanced-learning-update');
    ipcRenderer.removeAllListeners('content-switching-ready');
  },

  // Enhanced event listeners
  onEnhancedProgressUpdate: (callback) => {
    ipcRenderer.on('enhanced-progress-update', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('enhanced-progress-update', callback);
  },

  onEnhancedLogUpdate: (callback) => {
    ipcRenderer.on('enhanced-log-update', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('enhanced-log-update', callback);
  },

  onEnhancedPhaseUpdate: (callback) => {
    ipcRenderer.on('enhanced-phase-update', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('enhanced-phase-update', callback);
  },

  onEnhancedTrackerUpdate: (callback) => {
    ipcRenderer.on('enhanced-tracker-update', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('enhanced-tracker-update', callback);
  },

  onEnhancedLearningUpdate: (callback) => {
    ipcRenderer.on('enhanced-learning-update', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('enhanced-learning-update', callback);
  },

  // Pre-reporting data ready events
  onPreReportingDataReady: (callback) => {
    ipcRenderer.on('pre-reporting-data-ready', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('pre-reporting-data-ready', callback);
  },

  // Process waiting for user events
  onProcessWaitingForUser: (callback) => {
    ipcRenderer.on('process-waiting-for-user', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('process-waiting-for-user', callback);
  },

  onContentSwitchingReady: (callback) => {
    ipcRenderer.on('content-switching-ready', (event, data) => callback(data));
    return () => ipcRenderer.removeListener('content-switching-ready', callback);
  },

  // 🎯 PRODUCTION FIX: Tracker data management
  checkTrackerDataAvailability: () => ipcRenderer.invoke('check-tracker-data-availability'),
  populateTrackerFromComparison: () => ipcRenderer.invoke('populate-tracker-from-comparison')
});

// Expose Electron API for Data Builder and Bank Adviser
contextBridge.exposeInMainWorld('electronAPI', {
  // Data Builder API
  callDataBuilderAPI: (command, args) => ipcRenderer.invoke('call-data-builder-api', command, args),
  openFile: (filePath) => ipcRenderer.invoke('open-file', filePath),

  // Dialog API
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),

  // Bank Adviser API
  invoke: (channel, data) => {
    // Whitelist allowed Bank Adviser channels
    const allowedChannels = [
      'bank-adviser-create-session',
      'bank-adviser-update-session-sections',
      'bank-adviser-process-document',
      'bank-adviser-generate-excel',
      'bank-adviser-get-tracker-data',
      'bank-adviser-export-tracker',
      'bank-adviser-clear-table',
      'bank-adviser-delete-records',
      'bank-adviser-get-settings',
      'check-tracker-data-availability',
      'populate-tracker-from-comparison',
      'bulletproof-ui-activation',
      'open-file',
      'open-file-location',
      'show-save-dialog',
      'show-open-dialog'
    ];

    if (allowedChannels.includes(channel)) {
      return ipcRenderer.invoke(channel, data);
    } else {
      throw new Error(`Channel ${channel} not allowed`);
    }
  }
});

// Expose app events system
contextBridge.exposeInMainWorld('appEvents', {
  listeners: {},

  on: function(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  },

  off: function(event, callback) {
    if (!this.listeners[event]) return;
    this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
  },

  emit: function(event, data) {
    if (!this.listeners[event]) return;
    this.listeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error);
      }
    });
  }
});

console.log('THE PAYROLL AUDITOR - Enhanced Preload script loaded');
