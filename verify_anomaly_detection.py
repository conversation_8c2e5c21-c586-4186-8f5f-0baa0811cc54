#!/usr/bin/env python3
"""
Verify Anomaly Detection - Check if detection is working and examine page 463
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    return None

def verify_anomaly_detection():
    """Verify anomaly detection is working and check page 463"""
    print("🔍 ANOMALY DETECTION VERIFICATION")
    print("=" * 60)
    
    db_path = get_database_path()
    if not db_path:
        print("❌ Database not found")
        return
    
    print(f"✅ Database found: {db_path}")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Get latest session
            cursor.execute('SELECT session_id, status, created_at FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
            session = cursor.fetchone()
            
            if not session:
                print("❌ No sessions found")
                return
            
            session_id = session[0]
            print(f"📋 Latest session: {session_id}")
            print(f"📋 Status: {session[1]}")
            
            # Check if extraction_anomalies table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='extraction_anomalies'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                print("✅ extraction_anomalies table exists")
                
                # Check anomalies for this session
                cursor.execute('SELECT COUNT(*) FROM extraction_anomalies WHERE session_id = ?', (session_id,))
                anomaly_count = cursor.fetchone()[0]
                print(f"📊 Anomalies found for this session: {anomaly_count}")
                
                if anomaly_count > 0:
                    # Show anomalies
                    cursor.execute('''
                        SELECT employee_id, section_name, item_label, occurrence_count, severity, description
                        FROM extraction_anomalies 
                        WHERE session_id = ?
                        ORDER BY severity DESC, employee_id
                    ''', (session_id,))
                    anomalies = cursor.fetchall()
                    
                    print("\n🚨 DETECTED ANOMALIES:")
                    for emp_id, section, item, count, severity, desc in anomalies:
                        print(f"  {severity}: Employee {emp_id} - {section} - {item} ({count}x)")
                        print(f"    Description: {desc}")
                else:
                    print("ℹ️ No anomalies detected for this session")
            else:
                print("❌ extraction_anomalies table does not exist")
            
            # Check extracted_items table for page 463 specifically
            print(f"\n🔍 CHECKING PAGE 463 DATA:")
            
            # First, let's see if we have page information
            cursor.execute('''
                SELECT DISTINCT employee_id, section_name, item_label, item_value
                FROM extracted_items 
                WHERE session_id = ? AND employee_id LIKE '%463%'
                ORDER BY employee_id, section_name, item_label
            ''', (session_id,))
            page_463_data = cursor.fetchall()
            
            if page_463_data:
                print(f"✅ Found {len(page_463_data)} items for page 463")
                for emp_id, section, item, value in page_463_data:
                    print(f"  {emp_id}: {section} - {item} = {value}")
            else:
                print("❌ No data found for page 463")
                
                # Let's check what employee IDs we have
                cursor.execute('''
                    SELECT DISTINCT employee_id 
                    FROM extracted_items 
                    WHERE session_id = ?
                    ORDER BY employee_id
                    LIMIT 10
                ''', (session_id,))
                sample_employees = cursor.fetchall()
                
                print(f"\n📋 Sample employee IDs in database:")
                for (emp_id,) in sample_employees:
                    print(f"  {emp_id}")
            
            # Check for duplicates in LOANS section specifically
            print(f"\n🔍 CHECKING LOANS SECTION DUPLICATES:")
            cursor.execute('''
                SELECT employee_id, item_label, COUNT(*) as count, GROUP_CONCAT(item_value, ' | ') as all_values
                FROM extracted_items
                WHERE session_id = ? AND section_name = 'LOANS'
                GROUP BY employee_id, item_label
                HAVING COUNT(*) > 1
                ORDER BY COUNT(*) DESC
            ''', (session_id,))
            loans_duplicates = cursor.fetchall()
            
            if loans_duplicates:
                print(f"🚨 Found {len(loans_duplicates)} duplicate items in LOANS section:")
                for emp_id, item, count, all_values in loans_duplicates:
                    print(f"  Employee {emp_id}: {item} appears {count} times")
                    print(f"    Values: {all_values}")
            else:
                print("✅ No duplicates found in LOANS section")
            
            # Check all duplicates across all sections
            print(f"\n🔍 CHECKING ALL DUPLICATES:")
            cursor.execute('''
                SELECT employee_id, section_name, item_label, COUNT(*) as count, GROUP_CONCAT(item_value, ' | ') as all_values
                FROM extracted_items
                WHERE session_id = ?
                GROUP BY employee_id, section_name, item_label
                HAVING COUNT(*) > 1
                ORDER BY COUNT(*) DESC
                LIMIT 10
            ''', (session_id,))
            all_duplicates = cursor.fetchall()
            
            if all_duplicates:
                print(f"🚨 Found {len(all_duplicates)} duplicate items across all sections:")
                for emp_id, section, item, count, all_values in all_duplicates:
                    print(f"  Employee {emp_id}: {section} - {item} appears {count} times")
                    print(f"    Values: {all_values}")
            else:
                print("✅ No duplicates found in any section")
            
            # Check if anomaly detection phase ran
            print(f"\n🔍 CHECKING PHASE EXECUTION:")
            cursor.execute('''
                SELECT phase_name, status, completed_at
                FROM phase_execution_log 
                WHERE session_id = ?
                ORDER BY completed_at DESC
            ''', (session_id,))
            phases = cursor.fetchall()
            
            if phases:
                print("📋 Phase execution log:")
                for phase, status, completed in phases:
                    print(f"  {phase}: {status} at {completed}")
            else:
                print("❌ No phase execution log found")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_anomaly_detection()
