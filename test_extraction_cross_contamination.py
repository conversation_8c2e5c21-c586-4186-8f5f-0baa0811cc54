#!/usr/bin/env python3
"""
Test Extraction Cross-Contamination - Test the extraction on a specific problematic employee
"""

from perfect_section_aware_extractor import PerfectSectionAwareExtractor

def test_extraction_cross_contamination():
    print("🧪 TESTING EXTRACTION CROSS-CONTAMINATION")
    print("=" * 60)
    
    # Test with one of the problematic employees
    pdf_path = "Payslips/MN PAYSLIPS JUL 2025.pdf"
    
    # We need to find a page with one of the problematic employees
    # Let's test with a few pages to find the issue
    
    extractor = PerfectSectionAwareExtractor(debug=True)
    
    # Test pages around where we know there are issues
    test_pages = [100, 200, 300, 400, 500]
    
    for page_num in test_pages:
        print(f'\n🔍 TESTING PAGE {page_num}:')
        
        try:
            result = extractor.extract_perfect(pdf_path, page_num)
            
            if 'error' in result:
                print(f'❌ Error on page {page_num}: {result["error"]}')
                continue
            
            # Look for the cross-contamination pattern
            educational_items = []
            leave_items = []
            fuel_items = []
            
            for key, value in result.items():
                if 'EDUCATIONAL' in key.upper():
                    educational_items.append((key, value))
                elif 'LEAVE' in key.upper():
                    leave_items.append((key, value))
                elif 'FUEL' in key.upper():
                    fuel_items.append((key, value))
            
            # Check for cross-contamination
            issues_found = False
            
            for key, value in educational_items:
                if 'FUEL' in str(value).upper() or 'ELEMENT' in str(value).upper():
                    print(f'🚨 CROSS-CONTAMINATION: {key} = "{value}"')
                    issues_found = True
            
            for key, value in leave_items:
                if 'EDUCATIONAL' in str(value).upper():
                    print(f'🚨 CROSS-CONTAMINATION: {key} = "{value}"')
                    issues_found = True
            
            for key, value in fuel_items:
                if not extractor._is_likely_financial_amount(str(value)):
                    print(f'🚨 CROSS-CONTAMINATION: {key} = "{value}" (should be financial)')
                    issues_found = True
            
            if issues_found:
                print(f'🚨 CROSS-CONTAMINATION DETECTED ON PAGE {page_num}!')
                
                # Show all earnings items for analysis
                print(f'\n📋 ALL EARNINGS ITEMS ON PAGE {page_num}:')
                for key, value in result.items():
                    if any(section in key.upper() for section in ['EARNINGS', 'BASIC', 'LEAVE', 'FUEL', 'EDUCATIONAL']):
                        print(f'  {key}: "{value}"')
                
                return page_num  # Found a problematic page
            else:
                print(f'✅ No cross-contamination detected on page {page_num}')
                
        except Exception as e:
            print(f'❌ Exception on page {page_num}: {e}')
            continue
    
    print(f'\n❌ No cross-contamination found on tested pages')
    return None

def test_mock_cross_contamination():
    """Test with mock data to reproduce the issue"""
    print(f'\n🧪 TESTING WITH MOCK DATA:')
    print("=" * 60)
    
    extractor = PerfectSectionAwareExtractor(debug=True)
    
    # Create mock elements that might cause cross-contamination
    mock_elements = [
        {'text': 'LEAVE ALLOWANCE', 'x': 50, 'y': 200},
        {'text': '1,500.00', 'x': 200, 'y': 200},
        {'text': 'EDUCATIONAL SUPPORT', 'x': 300, 'y': 200},
        {'text': '2ND FUEL ELEMENT', 'x': 50, 'y': 220},
        {'text': '2,000.00', 'x': 200, 'y': 220},
        {'text': '1ST FUEL ELEMENT', 'x': 50, 'y': 240},
        {'text': '1,250.00', 'x': 200, 'y': 240},
    ]
    
    print(f'🧪 Testing with mock elements:')
    for elem in mock_elements:
        print(f'  "{elem["text"]}" at ({elem["x"]}, {elem["y"]})')
    
    # Test pairing
    pairs = extractor._find_label_value_pairs(mock_elements, 'EARNINGS')
    
    print(f'\n📊 Pairing results:')
    for pair in pairs:
        label_text = pair.get('label', {}).get('text', '')
        value_text = pair.get('value', {}).get('text', '')
        print(f'  "{label_text}" → "{value_text}"')
        
        # Check for cross-contamination
        if 'LEAVE' in label_text and 'EDUCATIONAL' in value_text:
            print(f'    🚨 CROSS-CONTAMINATION: Leave allowance paired with educational text!')
        elif 'EDUCATIONAL' in label_text and 'FUEL' in value_text:
            print(f'    🚨 CROSS-CONTAMINATION: Educational support paired with fuel element!')
        elif 'FUEL' in label_text and not extractor._is_likely_financial_amount(value_text):
            print(f'    🚨 CROSS-CONTAMINATION: Fuel element paired with non-financial value!')
        else:
            print(f'    ✅ Pairing looks correct')

if __name__ == "__main__":
    print("Testing extraction cross-contamination...")
    
    # Test 1: Real PDF pages
    problematic_page = test_extraction_cross_contamination()
    
    # Test 2: Mock data
    test_mock_cross_contamination()
    
    print(f'\n🎯 SUMMARY:')
    if problematic_page:
        print(f'Cross-contamination found on page {problematic_page}')
        print(f'The extraction algorithm is still creating wrong pairings')
    else:
        print(f'No cross-contamination found in tested pages')
        print(f'The issue might be in specific page layouts or data processing')
