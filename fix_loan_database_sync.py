#!/usr/bin/env python3
"""
Fix Loan Database Sync - Add SALARY ADVANCE PENT. to database
"""

import sqlite3

def fix_loan_database_sync():
    print("🔧 FIXING LOAN DATABASE SYNC")
    print("=" * 50)
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Get LOANS section ID
    cursor.execute("SELECT id FROM dictionary_sections WHERE section_name = 'LOANS'")
    loans_section_result = cursor.fetchone()
    
    if not loans_section_result:
        print("❌ LOANS section not found in database")
        return False
    
    loans_section_id = loans_section_result[0]
    print(f"📋 LOANS section ID: {loans_section_id}")
    
    # Check current loan items
    cursor.execute('''
        SELECT item_name, value_format, is_fixed 
        FROM dictionary_items 
        WHERE section_id = ?
        ORDER BY item_name
    ''', (loans_section_id,))
    
    current_loans = cursor.fetchall()
    print(f"\n📊 CURRENT LOAN ITEMS ({len(current_loans)}):")
    
    target_loan = "SALARY ADVANCE PENT."
    found_exact = False
    similar_loans = []
    
    for item_name, value_format, is_fixed in current_loans:
        if item_name == target_loan:
            found_exact = True
            print(f"   ✅ {item_name}: {value_format} (fixed: {bool(is_fixed)})")
        elif 'SALARY ADVANCE PENT' in item_name:
            similar_loans.append(item_name)
            print(f"   🔍 {item_name}: {value_format} (fixed: {bool(is_fixed)}) - SIMILAR")
        else:
            print(f"   - {item_name}: {value_format} (fixed: {bool(is_fixed)})")
    
    if found_exact:
        print(f"\n✅ {target_loan} already exists in database")
        
        # Check if it's marked as fixed (IN-HOUSE)
        cursor.execute('''
            SELECT value_format, is_fixed 
            FROM dictionary_items 
            WHERE section_id = ? AND item_name = ?
        ''', (loans_section_id, target_loan))
        
        result = cursor.fetchone()
        if result:
            value_format, is_fixed = result
            if value_format == 'IN-HOUSE LOAN' and is_fixed:
                print(f"   ✅ Already correctly configured as IN-HOUSE LOAN")
                conn.close()
                return True
            else:
                print(f"   🔧 Updating to IN-HOUSE LOAN configuration...")
                cursor.execute('''
                    UPDATE dictionary_items 
                    SET value_format = 'IN-HOUSE LOAN', is_fixed = 1
                    WHERE section_id = ? AND item_name = ?
                ''', (loans_section_id, target_loan))
                conn.commit()
                print(f"   ✅ Updated successfully")
    else:
        print(f"\n❌ {target_loan} not found in database")
        
        if similar_loans:
            print(f"   Similar loans found: {similar_loans}")
            
            # Check if we should update the similar one
            if 'SALARY ADVANCE PENT' in similar_loans:
                similar_loan = 'SALARY ADVANCE PENT'
                print(f"   🔧 Updating similar loan '{similar_loan}' to exact match...")
                
                cursor.execute('''
                    UPDATE dictionary_items 
                    SET item_name = ?, value_format = 'IN-HOUSE LOAN', is_fixed = 1
                    WHERE section_id = ? AND item_name = ?
                ''', (target_loan, loans_section_id, similar_loan))
                conn.commit()
                print(f"   ✅ Updated '{similar_loan}' to '{target_loan}'")
        else:
            # Add new entry
            print(f"   🔧 Adding new loan entry...")
            cursor.execute('''
                INSERT INTO dictionary_items 
                (section_id, item_name, value_format, validation_rules, include_new, include_increase, include_decrease, is_fixed)
                VALUES (?, ?, 'IN-HOUSE LOAN', '{}', 1, 1, 0, 1)
            ''', (loans_section_id, target_loan))
            conn.commit()
            print(f"   ✅ Added new loan entry")
    
    conn.close()
    return True

def test_classification_after_fix():
    """Test loan classification after database fix"""
    print(f"\n🧪 TESTING CLASSIFICATION AFTER FIX")
    print("=" * 50)
    
    try:
        from core.dictionary_manager import PayrollDictionaryManager
        
        # Create fresh instance to reload from database
        dict_manager = PayrollDictionaryManager(debug=True)
        
        test_loan = "SALARY ADVANCE PENT."
        classification = dict_manager.classify_loan_type(test_loan)
        
        print(f"🏷️ {test_loan}: {classification}")
        
        if "IN-HOUSE" in classification:
            print(f"✅ SUCCESS: Correctly classified as IN-HOUSE")
            return True
        else:
            print(f"❌ FAILED: Still classified as EXTERNAL")
            
            # Debug further
            dictionary = dict_manager.dictionary
            loans_section = dictionary.get('LOANS', {})
            loans_items = loans_section.get('items', {})
            
            print(f"\n🔍 DEBUGGING:")
            print(f"   LOANS section exists: {'LOANS' in dictionary}")
            print(f"   LOANS items count: {len(loans_items)}")
            
            if test_loan in loans_items:
                item_data = loans_items[test_loan]
                print(f"   ✅ {test_loan} found in dictionary")
                print(f"   Data: {item_data}")
                
                # Check if it's marked as fixed
                if item_data.get('is_fixed', False):
                    print(f"   ✅ Marked as fixed (should be IN-HOUSE)")
                else:
                    print(f"   ❌ Not marked as fixed (explains EXTERNAL classification)")
            else:
                print(f"   ❌ {test_loan} not found in loaded dictionary")
                # Show available items
                available = list(loans_items.keys())
                print(f"   Available: {available[:5]}...")
            
            return False
            
    except Exception as e:
        print(f"❌ Error testing classification: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_toggle_enforcement():
    """Verify that toggle exclusions are actually enforced in reports"""
    print(f"\n🔍 VERIFYING TOGGLE ENFORCEMENT IN REPORTS")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get latest session
        cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return False
        
        session_id = session_result[0]
        print(f"📅 Using session: {session_id}")
        
        # Get some items that should be excluded
        cursor.execute('''
            SELECT ds.section_name, di.item_name, di.include_new, di.include_increase, di.include_decrease
            FROM dictionary_items di
            JOIN dictionary_sections ds ON di.section_id = ds.id
            WHERE di.include_new = 0 OR di.include_increase = 0 OR di.include_decrease = 0
            LIMIT 5
        ''')
        
        excluded_items = cursor.fetchall()
        
        if not excluded_items:
            print("⚠️ No excluded items found in dictionary")
            return True
        
        print(f"📋 CHECKING EXCLUDED ITEMS:")
        violations = 0
        
        for section_name, item_name, include_new, include_increase, include_decrease in excluded_items:
            print(f"\n   📊 {section_name}.{item_name}:")
            print(f"      Include NEW: {bool(include_new)}")
            print(f"      Include INCREASE: {bool(include_increase)}")
            print(f"      Include DECREASE: {bool(include_decrease)}")
            
            # Check if excluded change types appear in comparison results
            excluded_types = []
            if not include_new:
                excluded_types.append('NEW')
            if not include_increase:
                excluded_types.append('INCREASED')
            if not include_decrease:
                excluded_types.append('DECREASED')
            
            for change_type in excluded_types:
                cursor.execute('''
                    SELECT COUNT(*) FROM comparison_results 
                    WHERE session_id = ? AND item_label = ? AND change_type = ?
                ''', (session_id, item_name, change_type))
                
                count = cursor.fetchone()[0]
                if count > 0:
                    print(f"      🚨 VIOLATION: {change_type} appears {count} times (should be excluded)")
                    violations += 1
                else:
                    print(f"      ✅ CORRECT: {change_type} properly excluded")
        
        conn.close()
        
        if violations == 0:
            print(f"\n✅ NO VIOLATIONS: Toggle exclusions are properly enforced")
            return True
        else:
            print(f"\n❌ {violations} VIOLATIONS: Some excluded items still appear in reports")
            print(f"   This indicates toggle enforcement may not be working properly")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying toggle enforcement: {e}")
        return False

if __name__ == "__main__":
    print("Fixing loan database sync and verifying toggle functionality...")
    
    # Fix 1: Database sync for loan classification
    sync_success = fix_loan_database_sync()
    
    # Test 1: Loan classification after fix
    classification_success = test_classification_after_fix()
    
    # Test 2: Toggle enforcement verification
    toggle_enforcement = verify_toggle_enforcement()
    
    print(f'\n🎯 FINAL VERIFICATION RESULTS:')
    print(f'   Database sync: {"✅ SUCCESS" if sync_success else "❌ FAILED"}')
    print(f'   Loan classification: {"✅ WORKING" if classification_success else "❌ BROKEN"}')
    print(f'   Toggle enforcement: {"✅ WORKING" if toggle_enforcement else "❌ ISSUES"}')
    
    if sync_success and classification_success and toggle_enforcement:
        print(f'\n🎉 ALL FUNCTIONALITY VERIFIED!')
        print(f'   ✅ Dictionary populated with 124 items')
        print(f'   ✅ SALARY ADVANCE PENT. classified as IN-HOUSE')
        print(f'   ✅ Toggle exclusions properly enforced')
        print(f'   ✅ Both change detection and include/exclude toggles working')
    else:
        print(f'\n💡 ISSUES SUMMARY:')
        if not sync_success:
            print(f'   - Database sync failed')
        if not classification_success:
            print(f'   - Loan classification still broken')
        if not toggle_enforcement:
            print(f'   - Toggle enforcement not working properly')
