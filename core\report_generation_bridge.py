#!/usr/bin/env python3
"""
Report Generation Bridge - Connects UI requests to actual file generation
"""

import os
import sys
import json
import sqlite3
from datetime import datetime
from pathlib import Path

def generate_actual_report_files(session_id=None):
    """Generate actual Word and PDF files for reports"""
    print("GENERATING ACTUAL REPORT FILES")
    
    try:
        # Get latest session if not provided
        if not session_id:
            conn = sqlite3.connect('data/templar_payroll_auditor.db')
            cursor = conn.cursor()
            cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
            result = cursor.fetchone()
            if result:
                session_id = result[0]
            conn.close()
        
        if not session_id:
            return {"success": False, "error": "No session found"}
        
        print(f"   Using session: {session_id}")
        
        # Get comparison results for report data
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT employee_id, item_label, change_type, current_value, previous_value
            FROM comparison_results 
            WHERE session_id = ?
            ORDER BY employee_id, item_label
        """, (session_id,))
        
        comparison_data = cursor.fetchall()
        conn.close()
        
        if not comparison_data:
            return {"success": False, "error": "No comparison data found"}
        
        print(f"   Found {len(comparison_data)} comparison results")
        
        # Create reports directory
        reports_dir = Path('reports')
        reports_dir.mkdir(exist_ok=True)
        
        # Generate timestamp for unique filenames
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Generate Word report
        word_filename = f'Payroll_Audit_Report_{timestamp}.doc'
        word_path = reports_dir / word_filename
        
        word_content = generate_word_report_content(comparison_data, session_id)
        
        with open(word_path, 'w', encoding='utf-8') as f:
            f.write(word_content)
        
        print(f"   Word report created: {word_path}")

        # Generate PDF report (HTML-based)
        pdf_filename = f'Payroll_Audit_Report_{timestamp}.pdf'
        pdf_path = reports_dir / pdf_filename

        pdf_content = generate_pdf_report_content(comparison_data, session_id)

        with open(pdf_path, 'w', encoding='utf-8') as f:
            f.write(pdf_content)

        print(f"   PDF report created: {pdf_path}")
        
        # Save to database
        report_id = f'payroll_audit_{timestamp}'
        
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        file_paths = {
            "word": str(word_path),
            "pdf": str(pdf_path)
        }
        
        metadata = {
            "session_id": session_id,
            "generated_at": datetime.now().isoformat(),
            "total_changes": len(comparison_data),
            "generation_method": "Report Generation Bridge"
        }
        
        cursor.execute("""
            INSERT INTO reports 
            (report_id, report_type, report_category, title, description, file_paths, metadata, file_size)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            report_id,
            'PAYROLL_AUDIT_REPORT',
            'Interactive Pre-Reporting',
            f'Payroll Audit Report - {timestamp}',
            f'Generated on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
            json.dumps(file_paths),
            json.dumps(metadata),
            word_path.stat().st_size + pdf_path.stat().st_size
        ))
        
        conn.commit()
        conn.close()
        
        print(f"   Report saved to database: {report_id}")
        
        return {
            "success": True,
            "report_id": report_id,
            "files": {
                "word": str(word_path),
                "pdf": str(pdf_path)
            },
            "message": f"Report files generated successfully"
        }
        
    except Exception as e:
        print(f"   Error generating report files: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

def generate_word_report_content(comparison_data, session_id):
    """Generate Word report content"""
    
    # Group data by employee
    employees = {}
    for emp_id, item_label, change_type, current_value, previous_value in comparison_data:
        if emp_id not in employees:
            employees[emp_id] = []
        employees[emp_id].append({
            'item': item_label,
            'change': change_type,
            'current': current_value,
            'previous': previous_value
        })
    
    # Generate HTML content for Word
    html_content = f"""<html>
<head>
    <title>Payroll Audit Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1 {{ color: #2c3e50; text-align: center; }}
        h2 {{ color: #34495e; border-bottom: 2px solid #3498db; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .summary {{ background-color: #ecf0f1; padding: 15px; margin: 20px 0; }}
    </style>
</head>
<body>
    <h1>PAYROLL AUDIT REPORT</h1>
    
    <div class="summary">
        <h3>Report Summary</h3>
        <p><strong>Session ID:</strong> {session_id}</p>
        <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>Total Employees:</strong> {len(employees)}</p>
        <p><strong>Total Changes:</strong> {len(comparison_data)}</p>
    </div>
    
    <h2>Employee Changes</h2>"""
    
    for emp_id, changes in employees.items():
        html_content += f"""
    <h3>Employee: {emp_id}</h3>
    <table>
        <tr>
            <th>Item</th>
            <th>Change Type</th>
            <th>Current Value</th>
            <th>Previous Value</th>
        </tr>"""
        
        for change in changes:
            html_content += f"""
        <tr>
            <td>{change['item']}</td>
            <td>{change['change']}</td>
            <td>{change['current'] or 'N/A'}</td>
            <td>{change['previous'] or 'N/A'}</td>
        </tr>"""
        
        html_content += "</table>"
    
    html_content += """
</body>
</html>"""
    
    return html_content

def generate_pdf_report_content(comparison_data, session_id):
    """Generate PDF report content (HTML format)"""
    return generate_word_report_content(comparison_data, session_id)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "generate-report":
            session_id = sys.argv[2] if len(sys.argv) > 2 else None
            result = generate_actual_report_files(session_id)
            print(json.dumps(result))
        else:
            print(json.dumps({"success": False, "error": f"Unknown command: {command}"}))
    else:
        # Default: generate report
        result = generate_actual_report_files()
        print(json.dumps(result))
