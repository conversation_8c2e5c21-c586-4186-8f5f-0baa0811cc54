#!/usr/bin/env python3
"""
Simple Auto-Learning Duplicate Check
"""

import sqlite3
import os

def simple_auto_learning_check():
    print("🔍 SIMPLE AUTO-LEARNING DUPLICATE CHECK")
    print("=" * 50)
    
    # Check database
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Get latest session
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    if not session_result:
        print('❌ No audit sessions found')
        return
    
    session_id = session_result[0]
    print(f'📅 Session: {session_id}')
    
    # Check auto_learning_results
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auto_learning_results'")
    if cursor.fetchone():
        cursor.execute('SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?', (session_id,))
        auto_count = cursor.fetchone()[0]
        print(f'📊 Auto-learning items: {auto_count}')
        
        if auto_count > 0:
            # Get sample items
            cursor.execute('''
                SELECT section_name, item_label, auto_approved 
                FROM auto_learning_results 
                WHERE session_id = ? 
                LIMIT 5
            ''', (session_id,))
            
            samples = cursor.fetchall()
            print(f'📋 Sample auto-learning items:')
            for section, item, auto_approved in samples:
                status = "AUTO-APPROVED" if auto_approved else "PENDING"
                print(f'   {section}.{item} ({status})')
    else:
        print(f'❌ auto_learning_results table not found')
    
    # Check dictionary_items
    cursor.execute('SELECT COUNT(*) FROM dictionary_items')
    dict_count = cursor.fetchone()[0]
    print(f'📊 Dictionary items: {dict_count}')
    
    if dict_count > 0:
        # Get sample items
        cursor.execute('''
            SELECT ds.section_name, di.item_name 
            FROM dictionary_items di
            JOIN dictionary_sections ds ON di.section_id = ds.id
            LIMIT 5
        ''')
        
        samples = cursor.fetchall()
        print(f'📋 Sample dictionary items:')
        for section, item in samples:
            print(f'   {section}.{item}')
    
    # Check for potential duplicates
    cursor.execute('''
        SELECT alr.section_name, alr.item_label
        FROM auto_learning_results alr
        WHERE alr.session_id = ?
        AND EXISTS (
            SELECT 1 FROM dictionary_items di
            JOIN dictionary_sections ds ON di.section_id = ds.id
            WHERE ds.section_name = alr.section_name 
            AND di.item_name = alr.item_label
        )
        LIMIT 10
    ''', (session_id,))
    
    duplicates = cursor.fetchall()
    
    if duplicates:
        print(f'\n🚨 FOUND {len(duplicates)} POTENTIAL DUPLICATES:')
        for section, item in duplicates:
            print(f'   {section}.{item}')
        print(f'\n❌ ISSUE: Items exist in both dictionary and auto-learning!')
    else:
        print(f'\n✅ NO DUPLICATES: Auto-learning items are unique')
    
    conn.close()
    
    # Check if duplicate prevention exists
    print(f'\n🔍 CHECKING DUPLICATE PREVENTION:')
    
    duplicate_checker_path = 'core/enhanced_duplicate_checker.py'
    if os.path.exists(duplicate_checker_path):
        print(f'✅ Enhanced duplicate checker exists')
        
        # Check phased process manager
        phased_manager_path = 'core/phased_process_manager.py'
        if os.path.exists(phased_manager_path):
            with open(phased_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'should_add_to_auto_learning' in content:
                print(f'✅ Phased manager uses duplicate prevention')
            else:
                print(f'❌ Phased manager NOT using duplicate prevention')
                print(f'💡 This explains why duplicates appear!')
        else:
            print(f'❌ Phased process manager not found')
    else:
        print(f'❌ Enhanced duplicate checker not found')
    
    return len(duplicates) == 0

if __name__ == "__main__":
    success = simple_auto_learning_check()
    
    if success:
        print(f'\n🎉 AUTO-LEARNING DUPLICATE PREVENTION WORKING!')
    else:
        print(f'\n⚠️ AUTO-LEARNING DUPLICATE PREVENTION NEEDS FIXING!')
        print(f'   Recommendation: Integrate duplicate prevention in auto-learning workflow')
