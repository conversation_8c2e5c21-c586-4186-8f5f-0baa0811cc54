<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Dialog Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 File Dialog Fix Test</h1>
        <p>This page tests the improved file dialog functionality with timeout handling and retry mechanism.</p>
        
        <h2>Test Cases:</h2>
        
        <button class="test-button" onclick="testBasicFileDialog()">
            📁 Test Basic File Dialog
        </button>
        
        <button class="test-button" onclick="testFileDialogWithRetry()">
            🔄 Test File Dialog with Retry
        </button>
        
        <button class="test-button" onclick="testMultipleDialogs()">
            📂 Test Multiple Dialogs
        </button>
        
        <button class="test-button" onclick="clearResults()">
            🗑️ Clear Results
        </button>
        
        <div id="results"></div>
    </div>

    <script>
        let testCount = 0;

        function addResult(message, type = 'info') {
            testCount++;
            const results = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>Test ${testCount}:</strong> ${message}`;
            results.appendChild(resultDiv);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testCount = 0;
        }

        async function testBasicFileDialog() {
            addResult('Testing basic file dialog...', 'info');
            
            try {
                if (!window.api || !window.api.selectPdfFile) {
                    throw new Error('File dialog API not available (not running in Electron)');
                }
                
                const startTime = Date.now();
                addResult('Opening file dialog... (please select a file or cancel)', 'info');
                
                const filePath = await window.api.selectPdfFile();
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (filePath) {
                    addResult(`✅ SUCCESS: File selected in ${duration}ms<br>Path: ${filePath}`, 'success');
                } else {
                    addResult(`ℹ️ No file selected (user canceled) in ${duration}ms`, 'info');
                }
                
            } catch (error) {
                addResult(`❌ ERROR: ${error.message}`, 'error');
            }
        }

        async function testFileDialogWithRetry() {
            addResult('Testing file dialog with retry mechanism...', 'info');
            
            try {
                if (!window.selectAuditFile) {
                    throw new Error('selectAuditFile function not available');
                }
                
                const startTime = Date.now();
                addResult('Opening file dialog with retry... (please select a file or cancel)', 'info');
                
                // This should use the enhanced selectAuditFile function
                await window.selectAuditFile('current');
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                addResult(`✅ SUCCESS: File dialog completed with retry mechanism in ${duration}ms`, 'success');
                
            } catch (error) {
                addResult(`❌ ERROR: ${error.message}`, 'error');
            }
        }

        async function testMultipleDialogs() {
            addResult('Testing multiple file dialogs in sequence...', 'info');
            
            try {
                if (!window.api || !window.api.selectPdfFile) {
                    throw new Error('File dialog API not available');
                }
                
                for (let i = 1; i <= 3; i++) {
                    addResult(`Opening dialog ${i}/3... (you can cancel to skip)`, 'info');
                    
                    const startTime = Date.now();
                    const filePath = await window.api.selectPdfFile();
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    if (filePath) {
                        addResult(`✅ Dialog ${i}: File selected in ${duration}ms`, 'success');
                    } else {
                        addResult(`ℹ️ Dialog ${i}: Canceled in ${duration}ms`, 'info');
                    }
                    
                    // Small delay between dialogs
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
                addResult('✅ Multiple dialog test completed', 'success');
                
            } catch (error) {
                addResult(`❌ ERROR in multiple dialog test: ${error.message}`, 'error');
            }
        }

        // Test if we're running in Electron
        window.addEventListener('DOMContentLoaded', () => {
            if (window.api && window.api.selectPdfFile) {
                addResult('✅ Running in Electron - File dialog API available', 'success');
            } else {
                addResult('⚠️ Not running in Electron - File dialog tests will fail', 'error');
            }
        });
    </script>
</body>
</html>
