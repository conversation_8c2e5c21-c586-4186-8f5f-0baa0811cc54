#!/usr/bin/env python3
"""
Item Duplication Anomaly Detection Configuration
Manages which items are allowed to appear multiple times vs. which should be flagged as anomalies
"""

class DuplicateItemConfig:
    """Configuration for item duplication detection and permissive items"""

    def __init__(self):
        # Items that are ALLOWED to appear multiple times in the same section
        # These will NOT be flagged as anomalies
        self.permissive_items = {
            'DEDUCTIONS': {
                # Insurance items that can legitimately appear multiple times
                'OLD MUTUAL LIFE ASSURANCE',
                'ENTERPRISE LIFE ASSURANCE',
                'GLICO LIFE ASSURANCE',
                'SIC LIFE ASSURANCE',
                'METROPOLITAN LIFE ASSURANCE',
                'STAR LIFE ASSURANCE',
                'DONEWELL LIFE ASSURANCE',

                # Loan items that might appear multiple times
                'LOAN REPAYMENT',
                'BANK LOAN',
                'PERSONAL LOAN',
                'CAR LOAN',
                'MORTGAGE LOAN',

                # Union dues that might have multiple entries
                'UNION DUES',
                'ASSOCIATION DUES',
                'PROFESSIONAL DUES',

                # Welfare contributions
                'WELFARE FUND',
                'BENEVOLENT FUND',
                'STAFF WELFARE',
            },

            'EARNINGS': {
                # Allowances that might appear multiple times
                'OVERTIME ALLOWANCE',
                'SHIFT ALLOWANCE',
                'WEEKEND ALLOWANCE',
                'HOLIDAY ALLOWANCE',

                # Performance bonuses
                'PERFORMANCE BONUS',
                'PRODUCTIVITY BONUS',
                'INCENTIVE BONUS',

                # Acting allowances
                'ACTING ALLOWANCE',
                'RESPONSIBILITY ALLOWANCE',
            },

            'LOANS': {
                # Different loan types that might appear multiple times
                'PERSONAL LOAN',
                'CAR LOAN',
                'MORTGAGE LOAN',
                'EMERGENCY LOAN',
                'SALARY ADVANCE',
            }
        }

        # Items that should NEVER appear multiple times (high priority anomalies)
        self.never_duplicate_items = {
            'PERSONAL DETAILS': {
                'EMPLOYEE NO.',
                'EMPLOYEE NAME',
                'DEPARTMENT',
                'SECTION',
                'JOB TITLE',
                'BANK',
                'ACCOUNT NO.',
                'BRANCH',
                'SSF NO.',
                'GHANA CARD ID',
            },

            'EARNINGS': {
                'BASIC SALARY',
                'BASIC PAY',
                'NET PAY',
                'GROSS PAY',
                'TOTAL EARNINGS',
            },

            'DEDUCTIONS': {
                'INCOME TAX',
                'PAYE',
                'SSF CONTRIBUTION',
                'TOTAL DEDUCTIONS',
                'NET DEDUCTION',
            }
        }

    def is_permissive_item(self, section_name: str, item_label: str) -> bool:
        """Check if an item is allowed to appear multiple times"""
        section_permissive = self.permissive_items.get(section_name.upper(), set())

        # Exact match
        if item_label.upper() in section_permissive:
            return True

        # Partial match for insurance items (handles variations)
        if section_name.upper() == 'DEDUCTIONS':
            for permissive_item in section_permissive:
                if 'LIFE ASSURANCE' in permissive_item and 'LIFE ASSURANCE' in item_label.upper():
                    return True
                if 'LOAN' in permissive_item and 'LOAN' in item_label.upper():
                    return True

        return False

    def get_anomaly_severity(self, section_name: str, item_label: str) -> str:
        """Determine the severity level of a duplication anomaly"""
        never_duplicate = self.never_duplicate_items.get(section_name.upper(), set())

        # Critical: Items that should never be duplicated
        if item_label.upper() in never_duplicate:
            return 'CRITICAL'

        # High: Financial amounts and important identifiers
        if any(keyword in item_label.upper() for keyword in ['SALARY', 'PAY', 'TAX', 'TOTAL', 'NET', 'GROSS']):
            return 'HIGH'

        # Moderate: Other earnings and deductions
        if section_name.upper() in ['EARNINGS', 'DEDUCTIONS']:
            return 'MODERATE'

        # Low: Everything else
        return 'LOW'

    def should_flag_as_anomaly(self, section_name: str, item_label: str, occurrence_count: int) -> bool:
        """Determine if item duplication should be flagged as an anomaly"""
        if occurrence_count <= 1:
            return False

        # Don't flag permissive items
        if self.is_permissive_item(section_name, item_label):
            return False

        # Flag everything else that appears more than once
        return True

    def get_anomaly_description(self, section_name: str, item_label: str, occurrence_count: int, amounts: list = None) -> str:
        """Generate a human-readable description of the anomaly"""
        severity = self.get_anomaly_severity(section_name, item_label)

        base_desc = f"Item '{item_label}' appears {occurrence_count} times in {section_name} section"

        if amounts and len(amounts) > 1:
            if all(amt == amounts[0] for amt in amounts):
                base_desc += f" with identical amounts ({amounts[0]})"
            else:
                base_desc += f" with different amounts ({', '.join(map(str, amounts))})"

        severity_desc = {
            'CRITICAL': 'This is highly suspicious and requires immediate attention',
            'HIGH': 'This is unusual and should be investigated',
            'MODERATE': 'This may indicate a data entry error',
            'LOW': 'This is worth reviewing but may be normal'
        }

        return f"{base_desc}. {severity_desc.get(severity, '')}"

# Global instance
duplicate_config = DuplicateItemConfig()

def is_permissive_duplicate(section_name: str, item_label: str) -> bool:
    """Global function to check if item is allowed to be duplicated"""
    return duplicate_config.is_permissive_item(section_name, item_label)

def should_flag_duplicate_anomaly(section_name: str, item_label: str, count: int) -> bool:
    """Global function to check if duplication should be flagged"""
    return duplicate_config.should_flag_as_anomaly(section_name, item_label, count)

def get_duplicate_anomaly_info(section_name: str, item_label: str, count: int, amounts: list = None) -> dict:
    """Global function to get complete anomaly information"""
    return {
        'is_anomaly': duplicate_config.should_flag_as_anomaly(section_name, item_label, count),
        'severity': duplicate_config.get_anomaly_severity(section_name, item_label),
        'description': duplicate_config.get_anomaly_description(section_name, item_label, count, amounts),
        'is_permissive': duplicate_config.is_permissive_item(section_name, item_label)
    }

import sqlite3
import os
from typing import Optional, Tuple

class EnhancedDuplicateChecker:
    """Enhanced duplicate checker for auto-learning system"""
    
    def __init__(self, database_path: Optional[str] = None):
        self.database_path = database_path or self._get_database_path()
        
    def _get_database_path(self) -> Optional[str]:
        """Get the database path"""
        db_paths = [
            r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
            r"data\templar_payroll_auditor.db",
            r"templar_payroll_auditor.db"
        ]
        
        for path in db_paths:
            if os.path.exists(path):
                return path
        return None
    
    def is_item_duplicate(self, section_name: str, item_name: str, session_id: str = None) -> Tuple[bool, str]:
        """
        Comprehensive duplicate check for auto-learning items
        
        Args:
            section_name: Section name (e.g., 'LOANS', 'EARNINGS')
            item_name: Item name to check
            session_id: Current session ID (optional)
            
        Returns:
            Tuple of (is_duplicate: bool, reason: str)
        """
        if not self.database_path:
            return False, "Database not available"
        
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # 1. Check if item exists in dictionary_items table
            cursor.execute("""
                SELECT di.id, di.item_name, di.auto_learned, ds.section_name
                FROM dictionary_items di
                JOIN dictionary_sections ds ON di.section_id = ds.id
                WHERE ds.section_name = ? AND di.item_name = ?
            """, (section_name, item_name))
            
            dict_result = cursor.fetchone()
            
            if dict_result:
                item_id, name, auto_learned, section = dict_result
                source = "auto-learned" if auto_learned else "manual"
                conn.close()
                return True, f"Already exists in dictionary_items (ID:{item_id}, Source:{source})"
            
            # 2. Check if item exists in dictionary_items with variations
            cursor.execute("""
                SELECT di.id, di.item_name, di.auto_learned, ds.section_name
                FROM dictionary_items di
                JOIN dictionary_sections ds ON di.section_id = ds.id
                WHERE ds.section_name = ? AND (
                    UPPER(di.item_name) = UPPER(?) OR
                    UPPER(di.standard_key) = UPPER(?)
                )
            """, (section_name, item_name, item_name))
            
            variation_result = cursor.fetchone()
            
            if variation_result:
                item_id, name, auto_learned, section = variation_result
                source = "auto-learned" if auto_learned else "manual"
                conn.close()
                return True, f"Similar item exists in dictionary_items: '{name}' (ID:{item_id}, Source:{source})"
            
            # 3. Check if item is already pending approval in auto_learning_results
            cursor.execute("""
                SELECT id, session_id, auto_approved, dictionary_updated, created_at
                FROM auto_learning_results
                WHERE section_name = ? AND item_label = ?
                ORDER BY created_at DESC
                LIMIT 1
            """, (section_name, item_name))
            
            pending_result = cursor.fetchone()
            
            if pending_result:
                result_id, result_session, approved, dict_updated, created = pending_result
                
                # If it's from the current session, it's definitely a duplicate
                if session_id and result_session == session_id:
                    conn.close()
                    return True, f"Already detected in current session (ID:{result_id})"
                
                # If it's approved but not yet transferred, it's a duplicate
                if approved and not dict_updated:
                    conn.close()
                    return True, f"Already approved, pending transfer (ID:{result_id}, Session:{result_session})"
                
                # If it's pending approval from a recent session (within 24 hours)
                try:
                    from datetime import datetime, timedelta
                    created_dt = datetime.fromisoformat(created.replace('Z', '+00:00'))
                    if datetime.now() - created_dt < timedelta(hours=24):
                        status = "approved" if approved else "pending"
                        conn.close()
                        return True, f"Recently {status} in session {result_session} (ID:{result_id})"
                except:
                    pass  # If datetime parsing fails, continue with other checks
            
            conn.close()
            return False, "Not a duplicate"
            
        except Exception as e:
            return False, f"Error checking duplicates: {e}"
    
    def should_add_to_auto_learning(self, section_name: str, item_name: str, session_id: str = None) -> Tuple[bool, str]:
        """
        Determine if an item should be added to auto-learning queue
        
        Args:
            section_name: Section name
            item_name: Item name
            session_id: Current session ID
            
        Returns:
            Tuple of (should_add: bool, reason: str)
        """
        is_duplicate, reason = self.is_item_duplicate(section_name, item_name, session_id)
        
        if is_duplicate:
            return False, f"Duplicate prevented: {reason}"
        else:
            return True, "New item, safe to add"
    
    def clean_duplicate_auto_learning_entries(self, session_id: str = None) -> int:
        """
        Clean duplicate entries from auto_learning_results table
        
        Args:
            session_id: Optional session ID to limit cleaning scope
            
        Returns:
            Number of duplicates removed
        """
        if not self.database_path:
            return 0
        
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Find duplicates in auto_learning_results where items already exist in dictionary_items
            query = """
                SELECT a.id, a.section_name, a.item_label
                FROM auto_learning_results a
                INNER JOIN dictionary_sections ds ON ds.section_name = a.section_name
                INNER JOIN dictionary_items di ON (
                    di.section_id = ds.id AND
                    di.item_name = a.item_label
                )
                WHERE a.auto_approved = 0 AND a.dictionary_updated = 0
            """
            
            params = []
            if session_id:
                query += ' AND a.session_id = ?'
                params.append(session_id)
            
            cursor.execute(query, params)
            duplicates = cursor.fetchall()
            
            if duplicates:
                # Remove the duplicates
                duplicate_ids = [str(dup[0]) for dup in duplicates]
                placeholders = ','.join(['?'] * len(duplicate_ids))
                
                cursor.execute(f"""
                    DELETE FROM auto_learning_results
                    WHERE id IN ({placeholders})
                """, duplicate_ids)
                
                conn.commit()
                
                print(f"   Cleaned {len(duplicates)} duplicate auto-learning entries")
                for dup in duplicates[:5]:  # Show first 5
                    print(f"     Removed: {dup[1]}.{dup[2]} (ID:{dup[0]})")
                
                if len(duplicates) > 5:
                    print(f"     ... and {len(duplicates) - 5} more")
            
            conn.close()
            return len(duplicates)
            
        except Exception as e:
            print(f"Error cleaning duplicates: {e}")
            return 0

# Global instance for easy access
duplicate_checker = EnhancedDuplicateChecker()

def is_item_duplicate(section_name: str, item_name: str, session_id: str = None) -> Tuple[bool, str]:
    """Global function for duplicate checking"""
    return duplicate_checker.is_item_duplicate(section_name, item_name, session_id)

def should_add_to_auto_learning(section_name: str, item_name: str, session_id: str = None) -> Tuple[bool, str]:
    """Global function for auto-learning addition check"""
    return duplicate_checker.should_add_to_auto_learning(section_name, item_name, session_id)

def clean_duplicate_auto_learning_entries(session_id: str = None) -> int:
    """Global function for cleaning duplicates"""
    return duplicate_checker.clean_duplicate_auto_learning_entries(session_id)
