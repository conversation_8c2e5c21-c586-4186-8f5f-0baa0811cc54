#!/usr/bin/env python3
"""
Investigate Report Generation - Check if reports are actually being generated
"""

import sqlite3
import os
import json
from datetime import datetime

def investigate_report_generation():
    """Investigate the complete report generation process"""
    print("🔍 INVESTIGATING REPORT GENERATION PROCESS")
    print("=" * 70)
    
    # Check 1: Reports directory
    print(f"\n📁 CHECKING REPORTS DIRECTORY:")
    reports_dir = 'reports'
    
    if os.path.exists(reports_dir):
        print(f"   ✅ Reports directory exists: {reports_dir}")
        
        # List files in reports directory
        files = os.listdir(reports_dir)
        if files:
            print(f"   📄 Found {len(files)} files in reports directory:")
            for file in files[:10]:  # Show first 10 files
                file_path = os.path.join(reports_dir, file)
                file_size = os.path.getsize(file_path)
                modified = datetime.fromtimestamp(os.path.getmtime(file_path))
                print(f"      - {file} ({file_size:,} bytes, modified: {modified})")
        else:
            print(f"   ❌ Reports directory is empty - no files generated")
    else:
        print(f"   ❌ Reports directory does not exist")
        print(f"   💡 This suggests reports are not being saved to filesystem")
    
    # Check 2: Database reports table
    print(f"\n🗄️ CHECKING DATABASE REPORTS TABLE:")
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Check if reports table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='reports'")
        if cursor.fetchone():
            print(f"   ✅ Reports table exists in database")
            
            # Get report count
            cursor.execute('SELECT COUNT(*) FROM reports')
            report_count = cursor.fetchone()[0]
            print(f"   📊 Total reports in database: {report_count}")
            
            if report_count > 0:
                # Get recent reports
                cursor.execute('''
                    SELECT report_id, report_type, report_category, title, file_paths, created_at
                    FROM reports 
                    ORDER BY created_at DESC 
                    LIMIT 5
                ''')
                
                recent_reports = cursor.fetchall()
                print(f"   📋 Recent reports:")
                
                for report_id, report_type, category, title, file_paths, created_at in recent_reports:
                    print(f"      - {report_id}: {title}")
                    print(f"        Type: {report_type} | Category: {category}")
                    print(f"        Created: {created_at}")
                    
                    # Check file paths
                    if file_paths:
                        try:
                            paths = json.loads(file_paths)
                            print(f"        File paths: {paths}")
                            
                            # Check if files actually exist
                            for format_type, file_path in paths.items():
                                if file_path and os.path.exists(file_path):
                                    file_size = os.path.getsize(file_path)
                                    print(f"          ✅ {format_type}: {file_path} ({file_size:,} bytes)")
                                else:
                                    print(f"          ❌ {format_type}: {file_path} (FILE NOT FOUND)")
                        except json.JSONDecodeError:
                            print(f"        ❌ Invalid file_paths JSON: {file_paths}")
                    else:
                        print(f"        ❌ No file paths stored")
                    print()
            else:
                print(f"   ❌ No reports found in database")
        else:
            print(f"   ❌ Reports table does not exist in database")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
    
    # Check 3: Report generation scripts
    print(f"\n🔧 CHECKING REPORT GENERATION SCRIPTS:")
    
    report_scripts = [
        'core/advanced_reporting_system.py',
        'ui/smart_report_generator.js',
        'ui/interactive_pre_reporting.js'
    ]
    
    for script in report_scripts:
        if os.path.exists(script):
            file_size = os.path.getsize(script)
            print(f"   ✅ {script}: {file_size:,} bytes")
        else:
            print(f"   ❌ {script}: NOT FOUND")
    
    # Check 4: Recent audit session data
    print(f"\n📊 CHECKING RECENT AUDIT SESSION DATA:")
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get latest session
        cursor.execute('SELECT session_id, created_at FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
        session_result = cursor.fetchone()
        
        if session_result:
            session_id, created_at = session_result
            print(f"   📅 Latest session: {session_id} (created: {created_at})")
            
            # Check comparison results (needed for reports)
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
            comparison_count = cursor.fetchone()[0]
            print(f"   📊 Comparison results: {comparison_count}")
            
            if comparison_count > 0:
                print(f"   ✅ Data available for report generation")
                
                # Check change types
                cursor.execute('''
                    SELECT change_type, COUNT(*) 
                    FROM comparison_results 
                    WHERE session_id = ? 
                    GROUP BY change_type
                ''', (session_id,))
                
                change_types = cursor.fetchall()
                print(f"   📋 Change types available:")
                for change_type, count in change_types:
                    print(f"      - {change_type}: {count}")
            else:
                print(f"   ❌ No comparison results - cannot generate reports")
        else:
            print(f"   ❌ No audit sessions found")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Session data error: {e}")

def test_report_generation_manually():
    """Test report generation manually to see what happens"""
    print(f"\n🧪 TESTING MANUAL REPORT GENERATION")
    print("=" * 70)
    
    try:
        # Try to import and use the advanced reporting system
        import sys
        sys.path.append('core')
        
        from advanced_reporting_system import AdvancedReportingSystem
        
        print(f"   ✅ Advanced reporting system imported successfully")
        
        # Create instance
        reporting_system = AdvancedReportingSystem()
        print(f"   ✅ Reporting system instance created")
        
        # Check available templates
        templates = reporting_system.templates
        print(f"   📋 Available templates: {list(templates.keys())}")
        
        # Try to generate a simple test report
        test_data = {
            'metadata': {
                'title': 'Test Report',
                'generated_at': datetime.now().isoformat()
            },
            'changes': []
        }
        
        # Create reports directory if it doesn't exist
        if not os.path.exists('reports'):
            os.makedirs('reports')
            print(f"   📁 Created reports directory")
        
        # Try generating a simple report
        output_path = 'reports/test_report.xlsx'
        
        if 'summary' in templates:
            result = reporting_system.generate_custom_report(
                test_data, 
                'summary', 
                output_path
            )
            
            print(f"   📄 Test report generation result: {result}")
            
            if result.get('success') and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"   ✅ Test report created: {output_path} ({file_size:,} bytes)")
                return True
            else:
                print(f"   ❌ Test report generation failed")
                return False
        else:
            print(f"   ❌ No suitable template found for testing")
            return False
            
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_report_generation_issues():
    """Diagnose why reports might not be generating"""
    print(f"\n🔍 DIAGNOSING REPORT GENERATION ISSUES")
    print("=" * 70)
    
    issues_found = []
    
    # Issue 1: Missing reports directory
    if not os.path.exists('reports'):
        issues_found.append("Reports directory does not exist")
    
    # Issue 2: No recent reports in database
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM reports WHERE created_at > datetime("now", "-1 day")')
        recent_reports = cursor.fetchone()[0]
        
        if recent_reports == 0:
            issues_found.append("No reports generated in the last 24 hours")
        
        conn.close()
    except:
        issues_found.append("Cannot access reports database")
    
    # Issue 3: Missing report generation scripts
    critical_scripts = [
        'core/advanced_reporting_system.py',
        'ui/smart_report_generator.js'
    ]
    
    for script in critical_scripts:
        if not os.path.exists(script):
            issues_found.append(f"Missing critical script: {script}")
    
    # Issue 4: No comparison data
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results')
        comparison_count = cursor.fetchone()[0]
        
        if comparison_count == 0:
            issues_found.append("No comparison results available for report generation")
        
        conn.close()
    except:
        issues_found.append("Cannot access comparison results")
    
    # Report issues
    if issues_found:
        print(f"   🚨 ISSUES FOUND ({len(issues_found)}):")
        for i, issue in enumerate(issues_found, 1):
            print(f"      {i}. {issue}")
        
        print(f"\n   💡 RECOMMENDATIONS:")
        print(f"      1. Ensure reports directory exists and is writable")
        print(f"      2. Run a complete audit to generate comparison data")
        print(f"      3. Check report generation scripts are present")
        print(f"      4. Verify database connectivity and schema")
        print(f"      5. Test report generation manually")
        
        return False
    else:
        print(f"   ✅ No obvious issues found")
        return True

if __name__ == "__main__":
    print("Investigating report generation process...")
    
    # Investigation 1: Check current state
    investigate_report_generation()
    
    # Investigation 2: Test manual generation
    manual_test_success = test_report_generation_manually()
    
    # Investigation 3: Diagnose issues
    no_issues = diagnose_report_generation_issues()
    
    print(f'\n🎯 INVESTIGATION SUMMARY:')
    print(f'   Manual test: {"✅ WORKING" if manual_test_success else "❌ FAILED"}')
    print(f'   System health: {"✅ HEALTHY" if no_issues else "❌ ISSUES FOUND"}')
    
    if manual_test_success and no_issues:
        print(f'\n🎉 REPORT GENERATION APPEARS TO BE WORKING!')
        print(f'   The issue may be in the UI integration or file path handling')
    else:
        print(f'\n⚠️ REPORT GENERATION HAS ISSUES!')
        print(f'   Reports may not be generating due to missing components or data')
