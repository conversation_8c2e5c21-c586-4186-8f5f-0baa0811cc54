{"DEDUCTIONS": {"items": {"BFUND": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "CHAIRMAN EDUC. FOUNDATIO": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "EDC GHANA BALANCE FUND": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "EDC GHANA FIXED INCOME FU": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "ENTERPRISE LIFE ASSURANCE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "FINANCE SELF HELP SCHEME": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "GHANA LIFE INSURANCE CO LT": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "GLICO LIFE INSURANCE COMP": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "INCOME TAX": {"value_format": "text", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "LEAVE REFUND": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "LOAN DEDUCTIONS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "MET INSURANCE POLICY": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": false, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "MIPLAN GROUP PREMIUM": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "MISSIONS SUPPORT": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "OLD MUTUAL LIFE ASSURANCE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "PRUDENTIAL LIFE INSURANCE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "PUC FEE SETTLEMENT": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "QUALITY LIFE ASSURANCE CO": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SEC. RET'T DONATION- 2 STAF": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "SIC LIFE COMPANY LIMITED": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "STAFF CREDIT UNION": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "STAFF CREDIT UNION REGIST": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "STAFF MUTUAL HEALTH FUND": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SUPPORT TO MISSIONS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "TAXABLE SALARY": {"value_format": "currency", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "TOTAL DEDUCTIONS": {"value_format": "currency", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "UNITED INVESTMENT TRUST": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "VANGUARD LIFE PROVIDENT F": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "CODE Z": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "COP HQ HUMAN RESOURCE W": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "COPHSWA WELFARE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "FINANCE STAFF WELFARE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "HUMAN RESOURCE WELFARE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "INTERNAL AUDIT WELFARE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "LEAVE ALLOWANCE MISSIONS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "LEAVE PENSIONS OFFICE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "MINISTERS PENSION": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "MINISTERS-PENSION": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "MISSIONS STAFF WELFARE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "PENT TV WELFARE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "PROVIDENT FUND": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "RENT ELEMENT - STAFF": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "RETAINABLE ALLOWANCE-HQ": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "RETAINABLE DED-INT. MISSIO": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SCHOLARSHIP FUND": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "SSF EMPLOYEE": {"value_format": "currency", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": true, "is_column_header": false}, "TITHES": {"value_format": "currency", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "TRANSPORT ELEMENT": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "TRANSPORT ELEMENT - MISSI": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "TRANSPORT ELEMENT - PENSI": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "TRANSPORT STAFF WELFARE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "WELFARE FUND": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": false, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "WOMENS WELFARE DEDUCTIO": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}}}, "EARNINGS": {"items": {"BASIC SALARY": {"value_format": "text", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "EDUCATIONAL SUBSIDY": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "EDUCATIONAL SUPPORT": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "GROSS SALARY": {"value_format": "currency", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "INS/ASSUCE/LOAN/ETC R": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "LEAVE ALLOWANCE INT": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "LEAVE PENSIONS ALLOW": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "LONG SERVICE AWARD 1": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "LONG SERVICE AWARDS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "MOTIVATIONAL ALLOW. P": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "MOTOR VEH. MAINTENAN": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "NET PAY": {"value_format": "currency", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "OUT OF STATION ALLOW": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "SALARY ARREARS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "TRANSFER GRANT": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "1ST FUEL ELEMENT": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "1ST FUEL ELEMENT - MIS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "1ST FUEL ELEMENT - PEN": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "2ND FUEL ELEMENT": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "2ND FUEL ELEMENT - MIS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "ALL INCLUSIVE ALL. MISS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "ALL INCLUSIVE PENT TV": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "ALLINCLUSIVE ALLOWAN": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "EXEC. DRIVERS ALL. MIS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "EXECUTIVE DRIVERS ALL": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "HEAD OF DEPT ALLOWAN": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "HONORARIUM": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "HOUSING ALLOWANCE  D": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "PROFESSIONAL ALLOWA": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "RENT ALLOWANCE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "RENT ELEMENT": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "RESPONSIBILITY ALLOW": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "RESPONSIBILITY ALLOW.": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "RESPONSIBILITY HEADS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "RISK ALLOWANCE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "RISK ALLOWANCE  STAFF": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": false, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "RISKSECURITY MEN": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SECONDMENT ALL.  CAT1": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SECONDMENT ALL.  PRE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SECURITY GUARD ALLO": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SUBSISTENCE ~ STAFF": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SUBSISTENCEAREA HEA": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SUBSISTENCEMINISTERS": {"value_format": "Numeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SUSTAINABILITY ALLOWA": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "VEHICLE MAINT ALL  STA": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "VEHICLE MAINTENANCE": {"value_format": "Numeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}}}, "EMPLOYEE BANK DETAILS": {"items": {"ACCOUNT NO.": {"value_format": "alphanumeric", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "BANK": {"value_format": "text", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "BRANCH": {"value_format": "text", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": false, "is_fixed": true, "is_column_header": false}}}, "EMPLOYERS CONTRIBUTION": {"items": {"SAVING SCHEME EMPLOYER": {"value_format": "currency", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "SSF EMPLOYER": {"value_format": "currency", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": true, "is_column_header": false}}}, "LOANS": {"items": {"BUILDING-MINISTERS": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "PENSIONS RENT ADVANCE": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "PENSIONS SALARY ADVA": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "RENT ADVANCE": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": false, "is_column_header": false}, "RENT ADVANCE MISSIONS": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SALARY ADVANCE MISSI": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SALARY ADVANCE MISSIONS": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SALARY-ADVANCE-MINS": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "STAFF CREDIT UNION LO": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SALARY ADVANCE-MINS": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "SALARY ADVANCE-STAFF": {"value_format": "IN-HOUSE LOAN", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "SALARY ADVANCE PENT": {"value_format": "number", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": true, "is_fixed": false, "is_column_header": false}, "SALARY ADVANCE PENT.": {"include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": true}}}, "PERSONAL DETAILS": {"items": {"GHANA CARD ID": {"value_format": "alphanumeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "SECTION": {"value_format": "text", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "SSF NO.": {"value_format": "alphanumeric", "validation_rules": "{}", "include_new": false, "include_increase": false, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "DEPARTMENT": {"value_format": "text", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "EMPLOYEE NAME": {"value_format": "text", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "EMPLOYEE NO.": {"value_format": "alphanumeric", "validation_rules": "{}", "include_new": true, "include_increase": true, "include_decrease": false, "is_fixed": true, "is_column_header": false}, "JOB TITLE": {"value_format": "text", "validation_rules": "{}", "include_new": false, "include_increase": true, "include_decrease": false, "is_fixed": true, "is_column_header": false}}}}