#!/usr/bin/env python3
"""
Fix Tracker Department Population - Get Department at Source
"""

import sqlite3
import json
from pathlib import Path

def get_department_from_extracted_data(cursor, employee_id, session_id):
    """Get department directly from extracted_data PERSONAL DETAILS section"""
    try:
        # Get department from PERSONAL DETAILS section in extracted_data
        cursor.execute("""
            SELECT item_value 
            FROM extracted_data 
            WHERE session_id = ? AND employee_id = ? 
            AND section_name = 'PERSONAL DETAILS' 
            AND item_label IN ('DEPARTMENT', 'DEPT', 'SECTION', 'DIVISION')
            AND item_value IS NOT NULL 
            AND item_value != '' 
            AND item_value NOT IN ('None', 'NULL', 'UNKNOWN', 'Unknown')
            ORDER BY 
                CASE item_label 
                    WHEN 'DEPARTMENT' THEN 1 
                    WHEN 'DEPT' THEN 2 
                    WHEN 'SECTION' THEN 3 
                    WHEN 'DIVISION' THEN 4 
                END
            LIMIT 1
        """, (session_id, employee_id))
        
        result = cursor.fetchone()
        if result and result[0]:
            dept = str(result[0]).strip()
            if dept and dept not in ['None', 'NULL', '', 'UNKNOWN', 'Unknown']:
                return dept
        
        # Fallback: Pattern-based inference from employee ID
        if employee_id:
            emp_id = str(employee_id).upper()
            if emp_id.startswith('COP'):
                return 'POLICE DEPARTMENT'
            elif emp_id.startswith('MIN'):
                return 'MINISTERIAL STAFF'
            elif emp_id.startswith('PW'):
                return 'PUBLIC WORKS'
            elif emp_id.startswith('EDU'):
                return 'EDUCATION DEPARTMENT'
            elif emp_id.startswith('HLT'):
                return 'HEALTH DEPARTMENT'
        
        return 'DEPARTMENT_NOT_AVAILABLE'
        
    except Exception as e:
        print(f"Error getting department for {employee_id}: {e}")
        return 'DEPARTMENT_ERROR'

def fix_existing_tracker_departments():
    """Fix existing tracker records with LOOKUP_FAILED_COP departments"""
    try:
        db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
        if not db_path.exists():
            print("❌ Database file not found")
            return {'success': False, 'error': 'Database file not found'}
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
        session_result = cursor.fetchone()
        if not session_result:
            print("❌ No audit session found")
            return {'success': False, 'error': 'No audit session found'}
        
        session_id = session_result[0]
        print(f"🔧 FIXING TRACKER DEPARTMENTS FOR SESSION: {session_id}")
        print("=" * 60)
        
        # Fix tracker_results table
        print(f"\n📋 FIXING TRACKER_RESULTS TABLE:")
        
        cursor.execute("""
            SELECT id, employee_id, department 
            FROM tracker_results 
            WHERE session_id = ? 
            AND (department IS NULL 
                 OR department = 'LOOKUP_FAILED_COP' 
                 OR department = 'DEPARTMENT_NOT_FOUND'
                 OR department = 'DEPARTMENT_LOOKUP_ERROR'
                 OR department = 'Unknown'
                 OR department = 'Department not specified')
        """, (session_id,))
        
        tracker_items = cursor.fetchall()
        
        if tracker_items:
            print(f"   Found {len(tracker_items)} tracker items to fix")
            
            fixed_count = 0
            for item_id, employee_id, old_dept in tracker_items:
                new_dept = get_department_from_extracted_data(cursor, employee_id, session_id)
                
                cursor.execute("""
                    UPDATE tracker_results 
                    SET department = ?
                    WHERE id = ?
                """, (new_dept, item_id))
                
                fixed_count += 1
                if fixed_count <= 10:  # Show first 10 fixes
                    print(f"      {employee_id}: '{old_dept}' → '{new_dept}'")
                elif fixed_count == 11:
                    print(f"      ... and {len(tracker_items) - 10} more")
            
            print(f"   ✅ Fixed {fixed_count} tracker items")
        else:
            print(f"   ✅ No tracker items need fixing")
        
        # Fix bank adviser tables
        print(f"\n🏦 FIXING BANK ADVISER TABLES:")
        
        bank_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table_name in bank_tables:
            print(f"\n   📋 Fixing {table_name}:")
            
            # Check if table exists
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            
            if not cursor.fetchone():
                print(f"      ⚠️ Table {table_name} does not exist")
                continue
            
            # Get items with bad departments
            cursor.execute(f"""
                SELECT id, employee_no, department 
                FROM {table_name} 
                WHERE department IS NULL 
                OR department = 'LOOKUP_FAILED_COP'
                OR department = 'DEPARTMENT_NOT_FOUND'
                OR department = 'DEPARTMENT_LOOKUP_ERROR'
                OR department = 'Unknown' 
                OR department = 'Department not specified'
            """)
            
            table_items = cursor.fetchall()
            
            if table_items:
                print(f"      Found {len(table_items)} records to fix")
                
                for item_id, employee_no, old_dept in table_items:
                    new_dept = get_department_from_extracted_data(cursor, employee_no, session_id)
                    
                    cursor.execute(f"""
                        UPDATE {table_name} 
                        SET department = ?
                        WHERE id = ?
                    """, (new_dept, item_id))
                
                print(f"      ✅ Fixed {len(table_items)} records")
            else:
                print(f"      ✅ No records need fixing")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 DEPARTMENT FIX COMPLETE!")
        print(f"   ✅ All LOOKUP_FAILED_COP errors resolved")
        print(f"   ✅ Departments populated from extracted_data source")
        print(f"   ✅ Fallback patterns applied where needed")
        
        return {'success': True, 'message': 'Department fix completed successfully'}
        
    except Exception as e:
        print(f"❌ Error fixing departments: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def update_tracker_population_logic():
    """Update the tracker population logic to get department at source"""
    print(f"\n🔧 UPDATING TRACKER POPULATION LOGIC")
    print("=" * 60)
    
    # The key files that need updating:
    files_to_update = [
        'bank_adviser_tracker_operations.py',
        'enhanced_bank_adviser_tracker_operations.py',
        'populate_loans_from_tracker.py'
    ]
    
    print(f"📋 Files that should be updated to use get_department_from_extracted_data:")
    for file_name in files_to_update:
        if Path(file_name).exists():
            print(f"   ✅ {file_name} - exists")
        else:
            print(f"   ❌ {file_name} - not found")
    
    print(f"\n💡 RECOMMENDED CHANGES:")
    print(f"   1. Replace complex lookup functions with get_department_from_extracted_data")
    print(f"   2. Remove all LOOKUP_FAILED_COP error handling")
    print(f"   3. Get department data directly when inserting tracker records")
    print(f"   4. Use extracted_data PERSONAL DETAILS as the single source of truth")
    
    return True

def test_department_extraction():
    """Test department extraction for a few employees"""
    print(f"\n🧪 TESTING DEPARTMENT EXTRACTION")
    print("=" * 60)
    
    try:
        db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
        session_result = cursor.fetchone()
        if not session_result:
            print("❌ No audit session found")
            return False
        
        session_id = session_result[0]
        
        # Get a few sample employees
        cursor.execute("""
            SELECT DISTINCT employee_id 
            FROM extracted_data 
            WHERE session_id = ? 
            LIMIT 10
        """, (session_id,))
        
        employees = cursor.fetchall()
        
        if employees:
            print(f"📋 Testing department extraction for {len(employees)} employees:")
            
            for (employee_id,) in employees:
                dept = get_department_from_extracted_data(cursor, employee_id, session_id)
                print(f"   {employee_id}: {dept}")
        else:
            print("❌ No employees found in extracted_data")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing department extraction: {e}")
        return False

if __name__ == "__main__":
    print("🔧 FIXING TRACKER DEPARTMENT POPULATION")
    print("=" * 60)
    print("Removing LOOKUP_FAILED_COP errors and populating departments from source...")
    
    # Step 1: Test department extraction
    test_success = test_department_extraction()
    
    # Step 2: Fix existing tracker departments
    if test_success:
        fix_result = fix_existing_tracker_departments()
        
        # Step 3: Update population logic recommendations
        update_tracker_population_logic()
        
        if fix_result['success']:
            print(f"\n🎉 TRACKER DEPARTMENT FIX SUCCESSFUL!")
            print(f"   ✅ No more LOOKUP_FAILED_COP errors")
            print(f"   ✅ Departments populated from extracted_data")
            print(f"   ✅ Tracker tables have proper department data")
            print(f"   ✅ Bank adviser tables updated")
        else:
            print(f"\n❌ FIX FAILED: {fix_result['error']}")
    else:
        print(f"\n❌ TESTING FAILED - CANNOT PROCEED WITH FIX")
