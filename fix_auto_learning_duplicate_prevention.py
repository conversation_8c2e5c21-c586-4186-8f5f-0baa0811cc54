#!/usr/bin/env python3
"""
Fix Auto-Learning Duplicate Prevention - Integrate enhanced duplicate checker
"""

import os

def fix_auto_learning_duplicate_prevention():
    """Fix the auto-learning system to use enhanced duplicate prevention"""
    print("🔧 FIXING AUTO-LEARNING DUPLICATE PREVENTION")
    print("=" * 60)
    
    phased_manager_path = 'core/phased_process_manager.py'
    
    if not os.path.exists(phased_manager_path):
        print(f'❌ Phased process manager not found: {phased_manager_path}')
        return False
    
    try:
        # Read the current file
        with open(phased_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if already fixed
        if 'should_add_to_auto_learning' in content:
            print(f'✅ Auto-learning duplicate prevention already implemented')
            return True
        
        print(f'🔍 Adding duplicate prevention to auto-learning system...')
        
        # Step 1: Add import at the top
        lines = content.split('\n')
        
        # Find the imports section
        import_index = -1
        for i, line in enumerate(lines):
            if line.startswith('from ') or line.startswith('import '):
                import_index = i
        
        if import_index != -1:
            # Add the enhanced duplicate checker import
            new_import = 'from core.enhanced_duplicate_checker import should_add_to_auto_learning, is_item_duplicate'
            lines.insert(import_index + 1, new_import)
            print(f'   ✅ Added enhanced duplicate checker import')
        else:
            print(f'   ❌ Could not find import section')
            return False
        
        # Step 2: Modify the _analyze_for_new_items method
        # Find the method
        method_start = -1
        for i, line in enumerate(lines):
            if 'def _analyze_for_new_items(' in line:
                method_start = i
                break
        
        if method_start == -1:
            print(f'   ❌ Could not find _analyze_for_new_items method')
            return False
        
        print(f'   ✅ Found _analyze_for_new_items method at line {method_start + 1}')
        
        # Step 3: Find the duplicate check location and replace it
        # Look for the line: if (section_name, item_label) not in known_items:
        duplicate_check_line = -1
        for i in range(method_start, len(lines)):
            if 'if (section_name, item_label) not in known_items:' in lines[i]:
                duplicate_check_line = i
                break
        
        if duplicate_check_line == -1:
            print(f'   ❌ Could not find duplicate check location')
            return False
        
        print(f'   ✅ Found duplicate check at line {duplicate_check_line + 1}')
        
        # Replace the simple check with enhanced duplicate prevention
        old_check = lines[duplicate_check_line]
        indent = len(old_check) - len(old_check.lstrip())
        
        new_check_lines = [
            ' ' * indent + '# Enhanced duplicate prevention - check if item should be added to auto-learning',
            ' ' * indent + 'should_add, reason = should_add_to_auto_learning(section_name, item_label, self.session_id)',
            ' ' * indent + 'if should_add:  # Only process if not already in dictionary'
        ]
        
        # Replace the old line with new lines
        lines[duplicate_check_line:duplicate_check_line+1] = new_check_lines
        
        print(f'   ✅ Replaced simple duplicate check with enhanced prevention')
        
        # Step 4: Add debug logging
        # Find the line after the new check where we create item_frequency entry
        for i in range(duplicate_check_line + 3, len(lines)):
            if 'if key not in item_frequency:' in lines[i]:
                # Add debug logging after this line
                debug_line_indent = len(lines[i]) - len(lines[i].lstrip()) + 4
                debug_line = ' ' * debug_line_indent + 'if self.debug_mode:'
                debug_msg = ' ' * (debug_line_indent + 4) + 'self._debug_print(f"✅ DUPLICATE PREVENTION: Adding new item {item_label} to auto-learning (reason: {reason})")'
                
                lines.insert(i + 1, debug_line)
                lines.insert(i + 2, debug_msg)
                print(f'   ✅ Added debug logging for duplicate prevention')
                break
        
        # Step 5: Write the modified content back
        modified_content = '\n'.join(lines)
        
        # Create backup first
        backup_path = f'{phased_manager_path}.backup_before_duplicate_fix'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f'   ✅ Created backup: {backup_path}')
        
        # Write the fixed version
        with open(phased_manager_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f'✅ Auto-learning duplicate prevention fix applied successfully!')
        
        return True
        
    except Exception as e:
        print(f'❌ Error fixing auto-learning duplicate prevention: {e}')
        import traceback
        traceback.print_exc()
        return False

def verify_fix():
    """Verify that the fix was applied correctly"""
    print(f'\n🧪 VERIFYING AUTO-LEARNING DUPLICATE PREVENTION FIX')
    print("=" * 60)
    
    phased_manager_path = 'core/phased_process_manager.py'
    
    try:
        with open(phased_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required components
        checks = [
            ('Enhanced duplicate checker import', 'should_add_to_auto_learning' in content),
            ('Import statement', 'from core.enhanced_duplicate_checker import' in content),
            ('Enhanced duplicate check', 'should_add, reason = should_add_to_auto_learning(' in content),
            ('Conditional processing', 'if should_add:  # Only process if not already in dictionary' in content),
            ('Debug logging', 'DUPLICATE PREVENTION: Adding new item' in content)
        ]
        
        all_passed = True
        for check_name, passed in checks:
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f'   {check_name}: {status}')
            if not passed:
                all_passed = False
        
        if all_passed:
            print(f'\n🎉 ALL VERIFICATION CHECKS PASSED!')
            print(f'   ✅ Enhanced duplicate prevention is now integrated')
            print(f'   ✅ Auto-learning will skip items already in dictionary')
            print(f'   ✅ Compute resources will be saved')
            print(f'   ✅ No more duplicate items in pending approval')
            return True
        else:
            print(f'\n❌ SOME VERIFICATION CHECKS FAILED')
            print(f'   Manual review of the fix may be required')
            return False
            
    except Exception as e:
        print(f'❌ Error verifying fix: {e}')
        return False

def show_fix_summary():
    """Show a summary of what the fix accomplishes"""
    print(f'\n📋 FIX SUMMARY')
    print("=" * 60)
    
    print(f'🎯 PROBLEM SOLVED:')
    print(f'   ❌ Before: Auto-learning added ALL extracted items to pending approval')
    print(f'   ❌ Before: Items already in dictionary appeared in pending approval')
    print(f'   ❌ Before: Wasted compute resources on known items')
    print(f'   ❌ Before: Users had to manually reject dictionary duplicates')
    
    print(f'\n✅ SOLUTION IMPLEMENTED:')
    print(f'   ✅ After: Auto-learning checks enhanced duplicate prevention first')
    print(f'   ✅ After: Items already in dictionary are automatically skipped')
    print(f'   ✅ After: Only truly new items appear in pending approval')
    print(f'   ✅ After: Compute resources are optimized')
    print(f'   ✅ After: Users only see genuinely new items for approval')
    
    print(f'\n🔧 TECHNICAL CHANGES:')
    print(f'   1. Added import: from core.enhanced_duplicate_checker import should_add_to_auto_learning')
    print(f'   2. Replaced simple dictionary check with enhanced duplicate prevention')
    print(f'   3. Added should_add_to_auto_learning() call before processing items')
    print(f'   4. Added debug logging for duplicate prevention actions')
    print(f'   5. Preserved all existing functionality while adding optimization')
    
    print(f'\n🚀 EXPECTED RESULTS:')
    print(f'   📊 Reduced pending approval items (only new items)')
    print(f'   ⚡ Faster auto-learning processing (skips known items)')
    print(f'   🎯 Better user experience (no duplicate approvals needed)')
    print(f'   💾 Optimized database usage (fewer unnecessary records)')

if __name__ == "__main__":
    print("Fixing auto-learning duplicate prevention issue...")
    
    # Apply the fix
    fix_success = fix_auto_learning_duplicate_prevention()
    
    if fix_success:
        # Verify the fix
        verify_success = verify_fix()
        
        # Show summary
        show_fix_summary()
        
        if verify_success:
            print(f'\n🎉 AUTO-LEARNING DUPLICATE PREVENTION FIX COMPLETE!')
            print(f'   The system will now skip items already in the dictionary')
            print(f'   Run a fresh audit to test the optimized auto-learning')
        else:
            print(f'\n⚠️ Fix applied but verification failed - manual review needed')
    else:
        print(f'\n❌ Fix failed - manual intervention required')
        print(f'   Check the error messages above for details')
