#!/usr/bin/env python3
"""
Bank Adviser Tracker Operations
Handles tracker table operations: clear table, delete records, duplicate checker
"""

import sys
import json
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main entry point for tracker operations"""
    
    if len(sys.argv) < 2:
        print(json.dumps({
            'success': False,
            'error': 'Command required. Available: clear_table, delete_records, check_duplicates'
        }))
        return
    
    command = sys.argv[1]
    
    try:
        from core.bank_adviser_tracker_manager import BankAdviserTrackerManager
        
        manager = BankAdviserTrackerManager(debug=False)
        
        if command == 'clear_table':
            if len(sys.argv) < 3:
                result = {'success': False, 'error': 'Category required for clear_table'}
            else:
                category = sys.argv[2]
                result = manager.clear_table(category)
        
        elif command == 'delete_records':
            if len(sys.argv) < 4:
                result = {'success': False, 'error': 'Category and record IDs required for delete_records'}
            else:
                category = sys.argv[2]
                record_ids = [int(id_str) for id_str in sys.argv[3:]]
                result = manager.delete_records(category, record_ids)
        
        elif command == 'check_duplicates':
            year = int(sys.argv[2]) if len(sys.argv) > 2 else None
            from core.duplicate_checker import DuplicateChecker
            checker = DuplicateChecker(debug=False)
            result = checker.scan_all_tables_for_duplicates(year)
        
        elif command == 'get_tracker_data':
            if len(sys.argv) < 3:
                result = {'success': False, 'error': 'Category required for get_tracker_data'}
            else:
                category = sys.argv[2]
                # Parse filters if provided
                filters = {}
                if len(sys.argv) > 3:
                    try:
                        filters = json.loads(sys.argv[3])
                    except:
                        pass
                result = manager.get_tracker_data(category, filters)

        elif command == 'populate_tables':
            result = populate_bank_adviser_tables()

        elif command == 'check_availability':
            result = check_tracker_data_availability()

        elif command == 'populate_from_comparison':
            result = populate_tracker_from_comparison()

        # 🗑️ REMOVED: bulletproof_activation - using simple approach

        else:
            result = {'success': False, 'error': f'Unknown command: {command}'}
        
        print(json.dumps(result))
    
    except Exception as e:
        print(json.dumps({
            'success': False,
            'error': str(e),
            'command': command
        }))

def get_employee_department(cursor, employee_id, session_id):
    """
    Get employee department from the employees table or extracted_data.

    Args:
        cursor: Database cursor
        employee_id: Employee ID to lookup
        session_id: Current session ID

    Returns:
        str: Employee department or fallback value
    """
    try:
        # First try to get department from employees table
        cursor.execute("""
            SELECT department FROM employees
            WHERE employee_id = ? AND session_id = ?
            AND department IS NOT NULL AND department != ''
            ORDER BY created_at DESC LIMIT 1
        """, (employee_id, session_id))

        result = cursor.fetchone()
        if result and result[0]:
            dept = str(result[0]).strip()
            if dept not in ['None', 'NULL', '', 'UNKNOWN']:
                return dept

        # Fallback: Try to get from extracted_data PERSONAL DETAILS section
        cursor.execute("""
            SELECT item_value FROM extracted_data
            WHERE employee_id = ? AND session_id = ?
            AND section_name = 'PERSONAL DETAILS'
            AND item_label IN ('DEPARTMENT', 'DEPT', 'SECTION', 'DIVISION')
            AND item_value IS NOT NULL AND item_value != ''
            ORDER BY
                CASE item_label
                    WHEN 'DEPARTMENT' THEN 1
                    WHEN 'DEPT' THEN 2
                    WHEN 'SECTION' THEN 3
                    WHEN 'DIVISION' THEN 4
                END
            LIMIT 1
        """, (employee_id, session_id))

        result = cursor.fetchone()
        if result and result[0]:
            dept = str(result[0]).strip()
            if dept not in ['None', 'NULL', '', 'UNKNOWN']:
                return dept

        # No fallback inference - return explicit indicator when department cannot be found
        return 'DEPARTMENT_NOT_FOUND'

    except Exception as e:
        print(f"Error getting department for {employee_id}: {e}", file=sys.stderr)
        return 'DEPARTMENT_LOOKUP_ERROR'

def get_employee_department_enhanced(cursor, employee_id, session_id):
    """
    Enhanced department lookup that captures department data at source during population

    Args:
        cursor: Database cursor
        employee_id: Employee ID to lookup
        session_id: Current session ID

    Returns:
        str: Employee department or derived value
    """
    try:
        # PRODUCTION FIX: Multi-source department lookup with simplified priority order

        # Priority 1: Current session extracted data (most recent)
        cursor.execute("""
            SELECT item_value FROM extracted_data
            WHERE employee_id = ? AND session_id = ?
            AND section_name = 'PERSONAL DETAILS'
            AND (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%'
                 OR item_label LIKE '%SECTION%' OR item_label LIKE '%DIVISION%')
            AND item_value IS NOT NULL AND item_value != ''
            AND item_value NOT IN ('None', 'NULL', 'UNKNOWN', 'N/A')
            ORDER BY
                CASE
                    WHEN item_label LIKE '%DEPARTMENT%' THEN 1
                    WHEN item_label LIKE '%DEPT%' THEN 2
                    WHEN item_label LIKE '%SECTION%' THEN 3
                    WHEN item_label LIKE '%DIVISION%' THEN 4
                    ELSE 5
                END
            LIMIT 1
        """, (employee_id, session_id))

        result = cursor.fetchone()
        if result and result[0]:
            dept = str(result[0]).strip()
            if dept and dept not in ['None', 'NULL', '', 'UNKNOWN', 'N/A']:
                return dept

        # Priority 2: Any session extracted data (fallback)
        cursor.execute("""
            SELECT item_value FROM extracted_data
            WHERE employee_id = ?
            AND section_name = 'PERSONAL DETAILS'
            AND (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%')
            AND item_value IS NOT NULL AND item_value != ''
            AND item_value NOT IN ('None', 'NULL', 'UNKNOWN', 'N/A')
            ORDER BY created_at DESC
            LIMIT 1
        """, (employee_id,))

        result = cursor.fetchone()
        if result and result[0]:
            dept = str(result[0]).strip()
            if dept and dept not in ['None', 'NULL', '', 'UNKNOWN', 'N/A']:
                return dept

        # Priority 3: Use a meaningful default instead of error
        return 'DEPARTMENT_PENDING_VERIFICATION'

    except Exception as e:
        print(f"Error in enhanced department lookup for {employee_id}: {e}", file=sys.stderr)
        return 'DEPARTMENT_LOOKUP_ERROR'

def populate_bank_adviser_tables():
    """Populate Bank Adviser tables from tracker results"""
    import sqlite3
    import sys
    import time
    from pathlib import Path

    try:
        # CRITICAL FIX: Use correct database path
        db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
        if not db_path.exists():
            return {'success': False, 'error': 'Database file not found'}

        # Implement retry mechanism for database locking
        max_retries = 5
        retry_delay = 1  # seconds
        conn = None

        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect(str(db_path), timeout=30.0)
                # Enable WAL mode for better concurrency
                conn.execute('PRAGMA journal_mode = WAL')
                conn.execute('PRAGMA busy_timeout = 30000')  # 30 second timeout
                cursor = conn.cursor()
                break
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                    print(f"Database locked, retrying in {retry_delay} seconds... (attempt {attempt + 1}/{max_retries})", file=sys.stderr)
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                    continue
                else:
                    raise

        if conn is None:
            return {'success': False, 'error': 'Failed to connect to database after multiple attempts'}

        # CRITICAL FIX: Get session with actual tracker data with fallback mechanisms
        current_session = None
        tracker_count = 0

        # Try to get the current active session first
        try:
            cursor.execute("SELECT session_id FROM current_session WHERE id = 1")
            active_session_result = cursor.fetchone()

            if active_session_result:
                active_session = active_session_result[0]
                # Check if this session has tracker data
                cursor.execute("""
                    SELECT COUNT(*) FROM tracker_results WHERE session_id = ?
                """, (active_session,))
                active_tracker_count = cursor.fetchone()[0]

                if active_tracker_count > 0:
                    current_session = active_session
                    tracker_count = active_tracker_count
                    print(f"Using active session {current_session} with {tracker_count} tracker items", file=sys.stderr)
                else:
                    print(f"Active session {active_session} has no tracker data, looking for alternatives", file=sys.stderr)
        except Exception as e:
            print(f"Error getting active session: {e}", file=sys.stderr)

        # Fallback: Get session with most tracker data
        if not current_session:
            cursor.execute("""
                SELECT session_id, COUNT(*) as tracker_count
                FROM tracker_results
                GROUP BY session_id
                ORDER BY tracker_count DESC, session_id DESC
                LIMIT 1
            """)

            result = cursor.fetchone()
            if not result:
                return {'success': False, 'error': 'No tracker data found in any session'}

            current_session = result[0]
            tracker_count = result[1]
            print(f"Using fallback session {current_session} with {tracker_count} tracker items", file=sys.stderr)

        # PRODUCTION FIX: Implement proper duplicate prevention
        # Clear existing data for this session AND check for duplicates across all sessions
        tables_to_clear = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']

        for table in tables_to_clear:
            # Clear data from this specific session
            cursor.execute(f"DELETE FROM {table} WHERE source_session = ?", (current_session,))

        print(f"Cleared existing data for session {current_session}", file=sys.stderr)

        # Populate in-house loans
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'IN_HOUSE_LOAN'
        """, (current_session,))

        in_house_data = cursor.fetchall()
        in_house_count = 0

        for row in in_house_data:
            try:
                employee_id = row[0]
                employee_name = row[1]
                loan_type = row[2]  # item_label
                if ' - ' in loan_type:
                    loan_type = loan_type.split(' - ')[0].strip()

                # PRODUCTION FIX: Check for existing duplicate before insertion
                cursor.execute("""
                    SELECT COUNT(*) FROM in_house_loans
                    WHERE employee_no = ? AND loan_type = ?
                    AND period_month = ? AND period_year = ?
                """, (employee_id, loan_type, '06', '2025'))

                existing_count = cursor.fetchone()[0]
                if existing_count > 0:
                    print(f"Skipping duplicate in-house loan: {employee_id} - {loan_type}", file=sys.stderr)
                    continue

                # Get actual employee department with enhanced lookup
                department = get_employee_department_enhanced(cursor, employee_id, current_session)

                cursor.execute("""
                    INSERT INTO in_house_loans
                    (employee_no, employee_name, department, loan_type, loan_amount,
                     period_month, period_year, period_acquired, source_session, remarks)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    employee_id, employee_name, department, loan_type, row[4] or 0,
                    '06', '2025', '2025-06', current_session, 'Monitoring'
                ))
                in_house_count += 1
            except Exception as e:
                print(f"Error inserting in-house loan for {row[0]}: {e}", file=sys.stderr)

        # Populate external loans
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'EXTERNAL_LOAN'
        """, (current_session,))

        external_data = cursor.fetchall()
        external_count = 0

        for row in external_data:
            try:
                employee_id = row[0]
                employee_name = row[1]
                loan_type = row[2]  # item_label
                if ' - ' in loan_type:
                    loan_type = loan_type.split(' - ')[0].strip()

                # PRODUCTION FIX: Check for existing duplicate before insertion
                cursor.execute("""
                    SELECT COUNT(*) FROM external_loans
                    WHERE employee_no = ? AND loan_type = ?
                    AND period_month = ? AND period_year = ?
                """, (employee_id, loan_type, '06', '2025'))

                existing_count = cursor.fetchone()[0]
                if existing_count > 0:
                    print(f"Skipping duplicate external loan: {employee_id} - {loan_type}", file=sys.stderr)
                    continue

                # Get actual employee department with enhanced lookup
                department = get_employee_department_enhanced(cursor, employee_id, current_session)

                cursor.execute("""
                    INSERT INTO external_loans
                    (employee_no, employee_name, department, loan_type, loan_amount,
                     period_month, period_year, period_acquired, source_session)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    employee_id, employee_name, department, loan_type, row[4] or 0,
                    '06', '2025', '2025-06', current_session
                ))
                external_count += 1
            except Exception as e:
                print(f"Error inserting external loan for {row[0]}: {e}", file=sys.stderr)

        # Populate motor vehicle maintenance
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'MOTOR_VEHICLE'
        """, (current_session,))

        motor_data = cursor.fetchall()
        motor_count = 0

        for row in motor_data:
            try:
                employee_id = row[0]
                employee_name = row[1]
                allowance_type = row[2]
                amount = row[4] or 0

                # PRODUCTION FIX: Check for existing duplicate before insertion
                cursor.execute("""
                    SELECT COUNT(*) FROM motor_vehicle_maintenance
                    WHERE employee_no = ? AND allowance_type = ?
                    AND period_month = ? AND period_year = ?
                """, (employee_id, allowance_type, '06', '2025'))

                existing_count = cursor.fetchone()[0]
                if existing_count > 0:
                    print(f"Skipping duplicate motor vehicle: {employee_id} - {allowance_type}", file=sys.stderr)
                    continue

                # Get actual employee department with enhanced lookup
                department = get_employee_department_enhanced(cursor, employee_id, current_session)

                cursor.execute("""
                    INSERT INTO motor_vehicle_maintenance
                    (employee_no, employee_name, department, allowance_type,
                     allowance_amount, payable_amount, maintenance_amount,
                     period_month, period_year, period_acquired,
                     source_session, remarks)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    employee_id, employee_name, department, allowance_type,
                    amount, amount, amount,
                    '06', '2025', '2025-06', current_session, 'Monitoring'
                ))
                motor_count += 1
            except Exception as e:
                print(f"Error inserting motor vehicle maintenance for {row[0]}: {e}", file=sys.stderr)

        conn.commit()

        total = in_house_count + external_count + motor_count
        return {
            'success': True,
            'in_house_loans': in_house_count,
            'external_loans': external_count,
            'motor_vehicles': motor_count,
            'total': total
        }

    except Exception as e:
        print(f"Error in populate_bank_adviser_tables: {e}", file=sys.stderr)
        return {'success': False, 'error': str(e)}
    finally:
        # Ensure database connection is always closed
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}", file=sys.stderr)

def check_tracker_data_availability():
    """🔍 Check availability of tracker data for UI activation"""
    try:
        import sqlite3

        db_path = 'data/templar_payroll_auditor.db'

        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            # Get the latest session
            cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
            session_result = cursor.fetchone()

            if not session_result:
                return {'success': False, 'error': 'No sessions found'}

            session_id = session_result[0]

            # Check tracker_results table
            cursor.execute('SELECT COUNT(*) FROM tracker_results WHERE session_id = ?', (session_id,))
            tracker_count = cursor.fetchone()[0]

            # Check Bank Adviser tables
            cursor.execute("""
                SELECT
                    (SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?) +
                    (SELECT COUNT(*) FROM external_loans WHERE source_session = ?) +
                    (SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?)
            """, (session_id, session_id, session_id))
            bank_adviser_count = cursor.fetchone()[0]

            total = tracker_count + bank_adviser_count

            return {
                'success': True,
                'session_id': session_id,
                'tracker_results': tracker_count,
                'bank_adviser_total': bank_adviser_count,
                'total': total,
                'available': total > 0
            }

    except Exception as e:
        return {'success': False, 'error': str(e)}

def populate_tracker_from_comparison():
    """🔄 Populate tracker data from comparison results"""
    try:
        import sqlite3

        db_path = 'data/templar_payroll_auditor.db'

        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            # Get the latest session
            cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
            session_result = cursor.fetchone()

            if not session_result:
                return {'success': False, 'error': 'No sessions found'}

            session_id = session_result[0]

            # Import and run tracker feeding
            try:
                from core.phased_process_manager import PhasedProcessManager

                manager = PhasedProcessManager()
                manager.session_id = session_id

                # Run tracker feeding phase
                success = manager._phase_tracker_feeding({})

                if success:
                    # Check results
                    cursor.execute('SELECT COUNT(*) FROM tracker_results WHERE session_id = ?', (session_id,))
                    tracker_count = cursor.fetchone()[0]

                    cursor.execute("""
                        SELECT
                            (SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?) +
                            (SELECT COUNT(*) FROM external_loans WHERE source_session = ?) +
                            (SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?)
                    """, (session_id, session_id, session_id))
                    bank_adviser_count = cursor.fetchone()[0]

                    return {
                        'success': True,
                        'session_id': session_id,
                        'tracker_results': tracker_count,
                        'bank_adviser_total': bank_adviser_count,
                        'total': tracker_count + bank_adviser_count
                    }
                else:
                    return {'success': False, 'error': 'Tracker feeding phase failed'}

            except Exception as e:
                return {'success': False, 'error': f'Tracker feeding error: {e}'}

    except Exception as e:
        return {'success': False, 'error': str(e)}

# 🗑️ REMOVED: bulletproof_ui_activation - using simple approach

def fallback_ui_activation(session_id=None):
    """🔄 Simple UI activation"""
    try:
        import sqlite3

        db_path = 'data/templar_payroll_auditor.db'

        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            if not session_id:
                # Get the latest session
                cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
                session_result = cursor.fetchone()

                if not session_result:
                    return {'success': False, 'error': 'No sessions found'}

                session_id = session_result[0]

            # Check data availability
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
            comparison_count = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (session_id,))
            pre_reporting_count = cursor.fetchone()[0]

            if comparison_count == 0:
                return {'success': False, 'error': 'No comparison data found'}

            if pre_reporting_count == 0:
                return {'success': False, 'error': 'No pre-reporting data found'}

            # Try to populate tracker data if missing
            tracker_result = populate_tracker_from_comparison()

            return {
                'success': True,
                'session_id': session_id,
                'comparison_count': comparison_count,
                'pre_reporting_count': pre_reporting_count,
                'tracker_populated': tracker_result.get('success', False),
                'tracker_total': tracker_result.get('total', 0)
            }

    except Exception as e:
        return {'success': False, 'error': str(e)}

if __name__ == '__main__':
    main()
