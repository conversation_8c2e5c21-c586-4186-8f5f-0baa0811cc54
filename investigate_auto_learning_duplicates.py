#!/usr/bin/env python3
"""
Investigate Auto-Learning Duplicate Prevention Issue
"""

import sqlite3
import json
import os
import sys

def investigate_auto_learning_duplicates():
    """Investigate why existing dictionary items appear in auto-learning pending approval"""
    print("🔍 INVESTIGATING AUTO-LEARNING DUPLICATE PREVENTION")
    print("=" * 70)
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Get latest session
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    if not session_result:
        print('❌ No audit sessions found')
        return False
    
    session_id = session_result[0]
    print(f'📅 Using session: {session_id}')
    
    # Check auto_learning_results table
    print(f'\n📋 AUTO-LEARNING RESULTS TABLE:')
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auto_learning_results'")
    if cursor.fetchone():
        cursor.execute('''
            SELECT section_name, item_label, confidence_score, auto_approved, dictionary_updated, created_at
            FROM auto_learning_results 
            WHERE session_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ''', (session_id,))
        
        auto_learning_items = cursor.fetchall()
        
        if auto_learning_items:
            print(f'Found {len(auto_learning_items)} auto-learning items:')
            for section, item, confidence, auto_approved, dict_updated, created in auto_learning_items:
                status = "AUTO-APPROVED" if auto_approved else "PENDING"
                dict_status = "UPDATED" if dict_updated else "NOT_UPDATED"
                print(f'   {section}.{item}: {confidence:.2f} ({status}, {dict_status})')
        else:
            print(f'❌ No auto-learning items found for current session')
    else:
        print(f'❌ auto_learning_results table not found')
    
    # Check dictionary_items table for comparison
    print(f'\n📋 DICTIONARY ITEMS TABLE:')
    cursor.execute('''
        SELECT ds.section_name, di.item_name, di.auto_learned, di.created_at
        FROM dictionary_items di
        JOIN dictionary_sections ds ON di.section_id = ds.id
        ORDER BY di.created_at DESC
        LIMIT 10
    ''')
    
    dict_items = cursor.fetchall()
    
    if dict_items:
        print(f'Found {len(dict_items)} dictionary items:')
        for section, item, auto_learned, created in dict_items:
            source = "AUTO-LEARNED" if auto_learned else "MANUAL"
            print(f'   {section}.{item}: {source}')
    else:
        print(f'❌ No dictionary items found')
    
    # Check for duplicates between auto-learning and dictionary
    print(f'\n🔍 CHECKING FOR DUPLICATES:')
    
    if auto_learning_items and dict_items:
        # Create sets for comparison
        auto_learning_set = set((section, item) for section, item, _, _, _, _ in auto_learning_items)
        dictionary_set = set((section, item) for section, item, _, _ in dict_items)
        
        duplicates = auto_learning_set.intersection(dictionary_set)
        
        if duplicates:
            print(f'🚨 FOUND {len(duplicates)} DUPLICATES:')
            for section, item in duplicates:
                print(f'   {section}.{item} - EXISTS IN BOTH DICTIONARY AND AUTO-LEARNING!')
                
                # Get details from both tables
                cursor.execute('''
                    SELECT confidence_score, auto_approved, dictionary_updated
                    FROM auto_learning_results 
                    WHERE session_id = ? AND section_name = ? AND item_label = ?
                ''', (session_id, section, item))
                
                auto_result = cursor.fetchone()
                
                cursor.execute('''
                    SELECT di.auto_learned, di.created_at
                    FROM dictionary_items di
                    JOIN dictionary_sections ds ON di.section_id = ds.id
                    WHERE ds.section_name = ? AND di.item_name = ?
                ''', (section, item))
                
                dict_result = cursor.fetchone()
                
                if auto_result and dict_result:
                    confidence, auto_approved, dict_updated = auto_result
                    auto_learned, dict_created = dict_result
                    
                    print(f'      Auto-learning: confidence={confidence:.2f}, approved={auto_approved}, updated={dict_updated}')
                    print(f'      Dictionary: auto_learned={auto_learned}, created={dict_created}')
            
            return False  # Duplicates found - system not working correctly
        else:
            print(f'✅ No duplicates found between auto-learning and dictionary')
            return True
    else:
        print(f'⚠️ Cannot check for duplicates - missing data in one or both tables')
        return True

def test_duplicate_prevention_logic():
    """Test the duplicate prevention logic directly"""
    print(f'\n🧪 TESTING DUPLICATE PREVENTION LOGIC')
    print("=" * 70)
    
    # Check if enhanced duplicate checker exists
    duplicate_checker_path = 'core/enhanced_duplicate_checker.py'
    
    if not os.path.exists(duplicate_checker_path):
        print(f'❌ Enhanced duplicate checker not found: {duplicate_checker_path}')
        return False
    
    print(f'✅ Enhanced duplicate checker found')
    
    # Test the duplicate checker
    try:
        sys.path.append('core')
        from enhanced_duplicate_checker import is_item_duplicate, should_add_to_auto_learning
        
        # Test with known existing items from dictionary
        test_items = [
            ('PERSONAL DETAILS', 'EMPLOYEE NAME'),
            ('PERSONAL DETAILS', 'DEPARTMENT'),
            ('EARNINGS', 'BASIC SALARY'),
            ('DEDUCTIONS', 'INCOME TAX'),
            ('LOANS', 'RENT ADVANCE')
        ]
        
        print(f'\n📋 Testing duplicate prevention with known dictionary items:')
        
        all_working = True
        for section, item in test_items:
            is_dup, dup_reason = is_item_duplicate(section, item)
            should_add, add_reason = should_add_to_auto_learning(section, item)
            
            print(f'   {section}.{item}:')
            print(f'      Is duplicate: {is_dup} ({dup_reason})')
            print(f'      Should add to auto-learning: {should_add} ({add_reason})')
            
            if is_dup and not should_add:
                print(f'      ✅ Duplicate prevention working correctly')
            else:
                print(f'      ❌ Duplicate prevention NOT working')
                all_working = False
        
        return all_working
        
    except Exception as e:
        print(f'❌ Error testing duplicate prevention logic: {e}')
        import traceback
        traceback.print_exc()
        return False

def check_auto_learning_integration():
    """Check if auto-learning system is using duplicate prevention"""
    print(f'\n🔍 CHECKING AUTO-LEARNING INTEGRATION')
    print("=" * 70)
    
    # Check phased_process_manager.py for duplicate prevention usage
    phased_manager_path = 'core/phased_process_manager.py'
    
    if not os.path.exists(phased_manager_path):
        print(f'❌ Phased process manager not found: {phased_manager_path}')
        return False
    
    print(f'✅ Phased process manager found')
    
    # Check if it imports and uses duplicate prevention
    try:
        with open(phased_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for imports
        has_duplicate_import = 'enhanced_duplicate_checker' in content or 'should_add_to_auto_learning' in content
        has_duplicate_usage = 'should_add_to_auto_learning(' in content or 'is_item_duplicate(' in content
        
        print(f'\n📋 Integration check:')
        print(f'   Has duplicate prevention import: {has_duplicate_import}')
        print(f'   Uses duplicate prevention functions: {has_duplicate_usage}')
        
        if has_duplicate_import and has_duplicate_usage:
            print(f'   ✅ Auto-learning system appears to use duplicate prevention')
            return True
        else:
            print(f'   ❌ Auto-learning system NOT using duplicate prevention')
            
            # Show where auto-learning items are added
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'auto_learning_results' in line and 'INSERT' in line:
                    print(f'   Found auto-learning insertion at line {i+1}:')
                    print(f'      {line.strip()}')
            
            return False
            
    except Exception as e:
        print(f'❌ Error checking auto-learning integration: {e}')
        return False

def fix_auto_learning_duplicate_prevention():
    """Fix the auto-learning system to use duplicate prevention"""
    print(f'\n🔧 FIXING AUTO-LEARNING DUPLICATE PREVENTION')
    print("=" * 70)
    
    phased_manager_path = 'core/phased_process_manager.py'
    
    try:
        with open(phased_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if already fixed
        if 'should_add_to_auto_learning' in content:
            print(f'✅ Auto-learning duplicate prevention already implemented')
            return True
        
        # Add import at the top
        lines = content.split('\n')
        
        # Find import section
        import_index = -1
        for i, line in enumerate(lines):
            if line.startswith('from ') or line.startswith('import '):
                import_index = i
        
        if import_index != -1:
            # Add the import
            new_import = 'from core.enhanced_duplicate_checker import should_add_to_auto_learning, is_item_duplicate'
            lines.insert(import_index + 1, new_import)
            print(f'✅ Added duplicate prevention import')
        
        # Find auto-learning item addition logic and add duplicate check
        for i, line in enumerate(lines):
            if 'auto_learning_results' in line and 'INSERT' in line:
                # Look for the context around this insertion
                context_start = max(0, i - 10)
                context_end = min(len(lines), i + 10)
                
                print(f'\n📋 Found auto-learning insertion at line {i+1}:')
                for j in range(context_start, context_end):
                    marker = ' >>> ' if j == i else '     '
                    print(f'{marker}{j+1}: {lines[j]}')
                
                # Add duplicate check before insertion
                # This would need to be customized based on the actual code structure
                print(f'\n💡 RECOMMENDATION:')
                print(f'   Add duplicate check before auto-learning insertion:')
                print(f'   ```python')
                print(f'   should_add, reason = should_add_to_auto_learning(section_name, item_label)')
                print(f'   if not should_add:')
                print(f'       continue  # Skip this item - already exists in dictionary')
                print(f'   ```')
                
                break
        
        # For now, just show the recommendation without modifying the file
        print(f'\n⚠️ Manual integration required - auto-learning logic needs custom modification')
        return False
        
    except Exception as e:
        print(f'❌ Error fixing auto-learning duplicate prevention: {e}')
        return False

if __name__ == "__main__":
    print("Investigating auto-learning duplicate prevention issue...")
    
    # Investigation 1: Check for actual duplicates
    no_duplicates = investigate_auto_learning_duplicates()
    
    # Investigation 2: Test duplicate prevention logic
    logic_working = test_duplicate_prevention_logic()
    
    # Investigation 3: Check integration
    integration_working = check_auto_learning_integration()
    
    # Fix 4: Attempt to fix the issue
    fix_applied = fix_auto_learning_duplicate_prevention()
    
    print(f'\n🎯 INVESTIGATION RESULTS:')
    print(f'   No duplicates in current data: {"✅ CLEAN" if no_duplicates else "❌ DUPLICATES FOUND"}')
    print(f'   Duplicate prevention logic: {"✅ WORKING" if logic_working else "❌ BROKEN"}')
    print(f'   Auto-learning integration: {"✅ INTEGRATED" if integration_working else "❌ NOT INTEGRATED"}')
    print(f'   Fix applied: {"✅ APPLIED" if fix_applied else "❌ MANUAL REQUIRED"}')
    
    if no_duplicates and logic_working and integration_working:
        print(f'\n🎉 AUTO-LEARNING DUPLICATE PREVENTION IS WORKING!')
        print(f'   ✅ No duplicates found in current session')
        print(f'   ✅ Duplicate prevention logic is functional')
        print(f'   ✅ Auto-learning system is properly integrated')
    else:
        print(f'\n💡 ISSUES IDENTIFIED:')
        if not no_duplicates:
            print(f'   - Duplicates exist between dictionary and auto-learning')
        if not logic_working:
            print(f'   - Duplicate prevention logic is not working correctly')
        if not integration_working:
            print(f'   - Auto-learning system is not using duplicate prevention')
        if not fix_applied:
            print(f'   - Manual integration of duplicate prevention required')
        
        print(f'\n🔧 RECOMMENDED ACTIONS:')
        print(f'   1. Integrate should_add_to_auto_learning() check in auto-learning workflow')
        print(f'   2. Add duplicate prevention before inserting items into auto_learning_results')
        print(f'   3. Test with a fresh audit to verify duplicate prevention works')
        print(f'   4. Monitor auto-learning pending items to ensure no dictionary duplicates')
