#!/usr/bin/env python3
"""
Diagnose Educational Subsidy Issue - Find root cause of COP3524 problem
"""

import sqlite3
from perfect_section_aware_extractor import PerfectSectionAwareExtractor

def diagnose_cop3524_issue():
    print("🔍 DIAGNOSING COP3524 EDUCATIONAL SUBSIDY ISSUE")
    print("=" * 60)
    
    # Get the PDF path and page number for COP3524
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Get latest session
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    if not session_result:
        print('❌ No audit sessions found')
        return
    
    session_id = session_result[0]
    print(f'📅 Using session: {session_id}')
    
    # Get the PDF path from the session
    cursor.execute('SELECT current_pdf_path FROM audit_sessions WHERE session_id = ?', (session_id,))
    pdf_result = cursor.fetchone()
    if not pdf_result:
        print('❌ No PDF path found for session')
        return
    
    pdf_path = pdf_result[0]
    print(f'📄 PDF Path: {pdf_path}')
    
    # Find COP3524's page number (we know it's around page 463 from earlier)
    test_pages = [463, 462, 464, 461, 465]
    
    extractor = PerfectSectionAwareExtractor(debug=True)
    
    for page_num in test_pages:
        print(f'\n🔍 TESTING PAGE {page_num} for COP3524...')
        
        try:
            # Extract raw data to see the actual pairing
            raw_data = extractor.extract_raw_data(pdf_path, page_num)
            
            if 'error' in raw_data:
                print(f'❌ Error on page {page_num}: {raw_data["error"]}')
                continue
            
            # Check if this page has COP3524
            personal_section = raw_data.get('sections', {}).get('PERSONAL DETAILS', {})
            extracted_pairs = personal_section.get('extracted_pairs', [])
            
            # Look for COP3524 in the pairs
            has_cop3524 = False
            for pair in extracted_pairs:
                label_text = pair.get('label', {}).get('text', '')
                value_text = pair.get('value', {}).get('text', '')
                
                if 'COP3524' in value_text or 'COP3524' in label_text:
                    has_cop3524 = True
                    break
            
            if has_cop3524:
                print(f'✅ FOUND COP3524 on page {page_num}!')
                
                # Analyze all sections for educational subsidy issues
                print(f'\n📋 ANALYZING ALL SECTIONS FOR EDUCATIONAL SUBSIDY ISSUES:')
                
                for section_name, section_data in raw_data.get('sections', {}).items():
                    pairs = section_data.get('extracted_pairs', [])
                    
                    print(f'\n🔍 {section_name} SECTION ({len(pairs)} pairs):')
                    
                    for pair in pairs:
                        label_text = pair.get('label', {}).get('text', '').strip()
                        value_text = pair.get('value', {}).get('text', '').strip()
                        
                        # Check for educational subsidy issues
                        if 'EDUCATIONAL' in label_text.upper():
                            print(f'  📚 EDUCATIONAL ITEM: {label_text} → {value_text}')
                            
                            # Check if value looks wrong
                            if not extractor._is_likely_financial_amount(value_text):
                                print(f'    🚨 ISSUE: Value "{value_text}" is not a financial amount!')
                                
                                # Check what this value actually is
                                if 'FUEL' in value_text.upper():
                                    print(f'    🚨 CRITICAL: Educational subsidy paired with fuel element!')
                                elif 'ELEMENT' in value_text.upper():
                                    print(f'    🚨 CRITICAL: Educational subsidy paired with another element!')
                        
                        elif 'BASIC SALARY' in label_text.upper():
                            print(f'  💰 BASIC SALARY: {label_text} → {value_text}')
                            
                            # Check if this is actually an educational subsidy amount
                            if 'EDUCATIONAL' in value_text.upper():
                                print(f'    🚨 CRITICAL: Basic Salary paired with educational text!')
                        
                        # Check for any cross-contamination
                        if ('EDUCATIONAL' in label_text.upper() and 'FUEL' in value_text.upper()) or \
                           ('BASIC' in label_text.upper() and 'EDUCATIONAL' in value_text.upper()):
                            print(f'    🚨 CROSS-CONTAMINATION: {label_text} ↔ {value_text}')
                
                # Test the perfect extraction to see final result
                print(f'\n🎯 TESTING PERFECT EXTRACTION RESULT:')
                perfect_result = extractor.extract_perfect(pdf_path, page_num)
                
                if 'error' not in perfect_result:
                    print(f'Perfect extraction items:')
                    for key, value in perfect_result.items():
                        if 'EDUCATIONAL' in key.upper() or 'BASIC SALARY' in key.upper():
                            print(f'  {key}: {value}')
                            
                            # Check for the specific issue
                            if 'EDUCATIONAL' in key.upper() and 'FUEL' in str(value).upper():
                                print(f'    🚨 CONFIRMED ISSUE: Educational subsidy has fuel element value!')
                
                return page_num  # Found the page
                
        except Exception as e:
            print(f'❌ Exception on page {page_num}: {e}')
            continue
    
    print('❌ COP3524 not found on any tested pages')
    return None

def analyze_extraction_algorithm():
    """Analyze the extraction algorithm for potential issues"""
    print(f'\n🔬 ANALYZING EXTRACTION ALGORITHM:')
    print("=" * 60)
    
    extractor = PerfectSectionAwareExtractor(debug=False)
    
    # Test the label-value pairing logic with mock data
    mock_elements = [
        {'text': 'EDUCATIONAL SUBSIDY', 'x': 50, 'y': 200},
        {'text': '1,500.00', 'x': 200, 'y': 200},
        {'text': '2ND FUEL ELEMENT', 'x': 300, 'y': 200},
        {'text': 'BASIC SALARY', 'x': 50, 'y': 220},
        {'text': '5,000.00', 'x': 200, 'y': 220},
    ]
    
    print(f'🧪 Testing with mock elements:')
    for elem in mock_elements:
        print(f'  {elem["text"]} at ({elem["x"]}, {elem["y"]})')
    
    # Test pairing
    pairs = extractor._find_label_value_pairs(mock_elements, 'EARNINGS')
    
    print(f'\n📊 Pairing results:')
    for pair in pairs:
        label_text = pair.get('label', {}).get('text', '')
        value_text = pair.get('value', {}).get('text', '')
        print(f'  {label_text} → {value_text}')
        
        # Check for issues
        if 'EDUCATIONAL' in label_text and 'FUEL' in value_text:
            print(f'    🚨 ISSUE REPRODUCED: Educational subsidy paired with fuel element!')

if __name__ == "__main__":
    # Step 1: Diagnose the specific COP3524 issue
    page_num = diagnose_cop3524_issue()
    
    # Step 2: Analyze the extraction algorithm
    analyze_extraction_algorithm()
    
    print(f'\n🎯 SUMMARY:')
    print(f'The issue appears to be in the label-value pairing logic where')
    print(f'educational subsidy labels are being paired with incorrect values')
    print(f'(like fuel elements) instead of their actual monetary amounts.')
