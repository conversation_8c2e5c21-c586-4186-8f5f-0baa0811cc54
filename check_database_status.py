#!/usr/bin/env python3
"""
Check Database Status - Simple diagnostic
"""

import sqlite3

def check_database():
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()

    print('=== DATABASE TABLES ===')
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
    tables = cursor.fetchall()
    for (table,) in tables:
        print(f'  {table}')

    print('\n=== LATEST SESSION ===')
    cursor.execute('SELECT session_id, status FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session = cursor.fetchone()
    if session:
        session_id = session[0]
        print(f'Session: {session_id}')
        print(f'Status: {session[1]}')
        
        print('\n=== EXTRACTED_ITEMS COUNT ===')
        cursor.execute('SELECT COUNT(*) FROM extracted_items WHERE session_id = ?', (session_id,))
        count = cursor.fetchone()[0]
        print(f'Total extracted items: {count}')
        
        if count > 0:
            print('\n=== SAMPLE EXTRACTED ITEMS ===')
            cursor.execute('SELECT employee_id, section_name, item_label, item_value FROM extracted_items WHERE session_id = ? LIMIT 10', (session_id,))
            items = cursor.fetchall()
            for emp_id, section, item, value in items:
                print(f'  {emp_id}: {section} - {item} = {value}')
        
        print('\n=== CHECK FOR DUPLICATES MANUALLY ===')
        cursor.execute('''
            SELECT employee_id, section_name, item_label, COUNT(*) as count
            FROM extracted_items 
            WHERE session_id = ?
            GROUP BY employee_id, section_name, item_label
            HAVING COUNT(*) > 1
            LIMIT 5
        ''', (session_id,))
        duplicates = cursor.fetchall()
        if duplicates:
            print('Found duplicates:')
            for emp_id, section, item, count in duplicates:
                print(f'  {emp_id}: {section} - {item} appears {count} times')
        else:
            print('No duplicates found')
            
        # Check if anomaly detection phase ran
        print('\n=== CHECK ANOMALY DETECTION PHASE ===')
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='extraction_anomalies'")
            table_exists = cursor.fetchone()
            if table_exists:
                print('✅ extraction_anomalies table exists')
                cursor.execute('SELECT COUNT(*) FROM extraction_anomalies WHERE session_id = ?', (session_id,))
                anomaly_count = cursor.fetchone()[0]
                print(f'Anomalies for this session: {anomaly_count}')
            else:
                print('❌ extraction_anomalies table does not exist - anomaly detection phase did not run')
        except Exception as e:
            print(f'Error checking anomaly table: {e}')

    conn.close()

if __name__ == "__main__":
    check_database()
